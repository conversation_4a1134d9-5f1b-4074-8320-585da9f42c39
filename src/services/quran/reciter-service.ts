import { readonly, ref } from 'vue';
import { useVerseStore } from '@/stores/quran/verse-store.ts';
import TavoosService from '@/services/service.ts';
import Verse from '@/models/quran/Verse.ts';

export type ReciterState = 'playing' | 'pending' | 'paused' | 'failed' | 'finished';
type ReciterRegion = 'this-page' | 'entire-quran' | 'custom';

interface ReciterSettings {
  readNumbers: boolean;
  region: ReciterRegion;
  startVerse: number;
  endVerse: number;
}

export default class Reciter extends TavoosService {
  private static _instance: Reciter;

  #currentVerse = ref<Verse>();
  #player = new Audio();
  #progress = ref(0);
  #settings = ref<ReciterSettings>();
  #state = ref<ReciterState>('pending');
  #verseStore = useVerseStore();

  collection: Verse[] = [];

  private constructor() {
    super();

    this.#player.addEventListener('emptied', () => (this.#progress.value = 0));

    this.#player.addEventListener('timeupdate', () => {
      this.#progress.value = this.#player.currentTime / this.#player.duration;
    });
  }

  public static get instance(): Reciter {
    Reciter._instance ??= new Reciter();

    return Reciter._instance;
  }

  get currentVerse() {
    return ref(this.#currentVerse);
  }

  get progress() {
    return readonly(ref(this.#progress.value));
  }

  get state() {
    return readonly(ref(this.#state.value));
  }

  get isActive() {
    return readonly(ref(this.isPlaying.value || this.isPaused.value));
  }

  get isPaused() {
    return readonly(ref(this.#state.value === 'paused'));
  }

  get isPlaying() {
    return readonly(ref(this.#state.value === 'playing'));
  }

  get settings() {
    const defaultSettings = {
      readNumbers: false,
      region: 'entire-quran',
      startVerse: 0,
      endVerse: 0,
    };

    this.#settings.value ??= JSON.parse(
      localStorage.getItem('reciterSettings') ?? JSON.stringify(defaultSettings),
    ) as ReciterSettings;

    return readonly(this.#settings.value);
  }

  set settings(value: ReciterSettings) {
    this.#settings.value = value;

    localStorage.setItem('reciterSettings', JSON.stringify(value));
  }

  get playList() {
    switch (this.settings.region) {
      case 'this-page':
        return this.collection;
      case 'entire-quran':
        return this.#verseStore.verses as Verse[];
      case 'custom': {
        const { startVerse = Number.POSITIVE_INFINITY, endVerse = Number.NEGATIVE_INFINITY } = this.#settings.value!;

        return this.#verseStore.verses.filter(verse => verse.indexInQuran >= startVerse && verse.indexInQuran <= endVerse) as Verse[];
      }
      default:
        return [];
    }
  }

  async #readAloudNumber(number: number) {
    if (!this.#settings.value?.readNumbers) return;

    if (number < 20 || number % 10 === 0) {
      await this.#readAloundNumberPart(number, false);
    } else if (number < 100) {
      await this.#readAloundNumberPart(number - (number % 10));
      await this.#readAloudNumber(number % 10);
    } else {
      await this.#readAloundNumberPart(number - (number % 100));
      await this.#readAloudNumber(number % 100);
    }
  }

  async #readAloundNumberPart(part: number, shouldConnect: boolean = true) {
    const audioHost = 'https://file-keeper.com/media/audio/numerals/persian-2';
    const fileName = `${part}${shouldConnect ? '_' : ''}.mp3`;
    const url = `${audioHost}/${fileName}`;

    return new Promise(resolve => {
      this.#player.addEventListener('ended', resolve, { once: true });
      this.#play(url);
    });
  }

  playOrPause(verse: Verse | undefined = undefined) {
    if (verse && verse !== this.#currentVerse.value) {
      this.#playVerse(verse);

      return;
    }

    switch (this.#state.value) {
      case 'playing':
        this.#pause();
        break;
      case 'paused':
        this.#resume();
        break;
      case 'pending':
        this.#playVerse(verse);
        break;
      default:
        break;
    }
  }

  #play(url: string) {
    this.#player.pause();
    this.#player.setAttribute('src', url);
    this.#resume();
  }

  #pause() {
    this.#player.pause();
    this.#state.value = 'paused';
  }

  #resume() {
    this.#player.play();
    this.#state.value = 'playing';
  }

  #playVerse(verse: Verse | undefined = undefined) {
    this.#player.removeEventListener('ended', this.#reciteNextVerse);

    if (verse && this.playList.includes(verse)) {
      this.#currentVerse.value = verse;
    }

    this.#currentVerse.value ??= this.playList[0];

    const recitationHost = 'https://file-keeper.com/media/audio/recitation/mohammad-seddigh-menshawi/morattal';
    const fileName = `${this.#currentVerse.value?.indexInQuran}.mp3`;

    this.#player.addEventListener('ended', this.#reciteNextVerse, { once: true });
    this.#play(`${recitationHost}/${fileName}`);
  }

  #reciteNextVerse = async () => {
    const currentVerse = this.#currentVerse.value!;

    await this.#readAloudNumber(currentVerse.index);

    const currentIndex = this.playList.indexOf(currentVerse);
    const nextIndex = (currentIndex + 1) % this.playList.length;

    this.#currentVerse.value = this.playList[nextIndex];

    this.#playVerse();
  };

  flush() {
    this.#pause();
    this.#player.removeEventListener('ended', this.#reciteNextVerse);
  }
}
