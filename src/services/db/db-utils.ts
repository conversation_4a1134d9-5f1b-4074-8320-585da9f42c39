import Api from '../api/api.ts';
import { runWhenIdle } from '@/utils/utils.ts';

function dbRequest(name: string, version: number) {
  return window.indexedDB.open(name, version);
}

type PopulateStoreOptions<T> = {
  expectedCount: number;
  objectStore: () => IDBObjectStore;
  fetchFromAPI: () => Promise<any[]>;
  transformRecord?: (record: any) => T;
  storeInMemory: (items: T[]) => void;
  storeInDBTransform?: (record: any) => any;
  writeChunkSize?: number;
  idleProcessor?: (records: any[], callback: (items: T[]) => void) => void;
};
export default class DbUtils {
  static async populateStore<T>(options: PopulateStoreOptions<T>) {
    options.transformRecord ??= record => record;
    options.storeInDBTransform ??= record => record;

    options.objectStore().getAll().onsuccess = async (event: any) => {
      const cachedData = event.target.result;

      if (cachedData.length === options.expectedCount) {
        if (options.idleProcessor) {
          options.idleProcessor(cachedData, options.storeInMemory);
        } else {
          const instances = cachedData.map(options.transformRecord);
          options.storeInMemory(instances);
        }
        return;
      }

      options.objectStore().clear();

      try {
        const freshData = await options.fetchFromAPI();

        if (options.idleProcessor) {
          options.idleProcessor(freshData, options.storeInMemory);
        } else {
          const instances = freshData.map(options.transformRecord!);
          options.storeInMemory(instances);
        }

        DbUtils.writeToIndexedDBInChunks(
          freshData,
          options.objectStore,
          options.storeInDBTransform!,
          options.writeChunkSize,
        );
      } catch (exc) {
        console.log('Error populating store:', exc);
      }
    };
  }

  static writeToIndexedDBInChunks<T>(
    data: T[],
    getStore: () => IDBObjectStore,
    transform: (item: T) => unknown,
    chunkSize = 100,
  ) {
    let index = 0;

    function processChunk() {
      const store = getStore();
      const end = Math.min(index + chunkSize, data.length);

      for (let i = index; i < end; i++) {
        const record = transform(data[i]);
        const req = store.put(record);
        req.onsuccess = () => (req.onsuccess = null);
      }

      index = end;

      if (index < data.length) runWhenIdle(processChunk);
    }

    processChunk();
  }

  static async fetchFromAPI<T>(service: string, endpoint: string, sortFn: (a: T, b: T) => number): Promise<T[]> {
    try {
      const url = `${new Api().serviceURL(service)}/${endpoint}`;
      const response = await fetch(url);
      const payload = (await response.json()) as T[];
      return payload.sort(sortFn);
    } catch (exc) {
      console.error(`Error fetching ${endpoint} from API:`, exc);
      return [];
    }
  }

  static objectStore(db: IDBDatabase, type: string): IDBObjectStore {
    try {
      return db.transaction([type], 'readwrite').objectStore(type);
    } catch (exc) {
      console.log(`Error while getting objectStore ${type}.`, exc);
      throw exc;
    }
  }

  static createRecordsInIdle<T>(recordsData: any[], transform: (record: any) => T, onComplete: (records: T[]) => void) {
    const result: T[] = [];
    let index = 0;
    const chunkSize = 50;

    function processChunk() {
      const end = Math.min(index + chunkSize, recordsData.length);
      for (; index < end; index++) {
        const record = recordsData[index];
        result.push(transform(record));
      }

      if (index < recordsData.length) {
        runWhenIdle(processChunk);
      } else {
        onComplete(result);
      }
    }

    processChunk();
  }

  static openDB(name: string, version: number, objectStoreNames: string[]) {
    const request = dbRequest(name, version);

    return new Promise<IDBDatabase>((resolve, reject) => {
      request.onblocked = event => {
        console.log('Database blocked:', event);
        reject(event);
      };

      request.addEventListener('error', event => {
        const errorCode = (event.target as any)?.errorCode;

        if (errorCode !== undefined) {
          console.error(`Database error: ${errorCode}`);
          reject(new Error(`Database error: ${errorCode}`));
          return;
        }

        (async () => {
          try {
            const databases = await window.indexedDB.databases();
            const names = databases.map(db => db.name);

            if (!names.includes(name)) {
              reject(new Error(`Database ${name} not found.`));
              return;
            }

            window.indexedDB.deleteDatabase(name);

            try {
              const reopenedDB = await DbUtils.openDB(name, version, objectStoreNames);
              resolve(reopenedDB);
            } catch (reopenErr) {
              reject(reopenErr);
            }
          } catch (exc: any) {
            if (exc instanceof DOMException && exc.message === 'Internal error.') {
              alert('You need to restart your browser due to an internal issue. Probably caused by low disk space.');
            } else {
              alert('Something went wrong with the Quran database. Please restart your browser.');
            }

            reject(exc);
          }
        })();
      });

      request.addEventListener('success', event => {
        resolve((event.target as IDBOpenDBRequest).result);
      });

      request.addEventListener('upgradeneeded', (event: IDBVersionChangeEvent) => {
        const database = (event.target as IDBOpenDBRequest).result as IDBDatabase;

        objectStoreNames.forEach(name => {
          try {
            database.deleteObjectStore(name);
          } catch (err) {
            // Store doesn't exist yet, which is fine
          }

          try {
            database.createObjectStore(name, { keyPath: 'id' });
          } catch (exc) {
            console.log('Error while upgrading database', { exc });
          }
        });
      });
    });
  }
}
