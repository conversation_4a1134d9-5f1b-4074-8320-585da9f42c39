import { useChapterStore } from '@/stores/quran/chapter-store.ts';
import { useVerseStore } from '@/stores/quran/verse-store.ts';
import { usePageStore } from '@/stores/quran/page-store.ts';
import { useProcessStore } from '@/stores/ProcessStore.ts';
import Api from '@/services/api/api.ts';
import TavoosService from '@/services/service.ts';
import Chapter from '@/models/quran/Chapter.ts';
import Verse from '@/models/quran/Verse.ts';
import Page, { TOTAL_PAGES } from '@/models/quran/Page.ts';
import DbUtils from '../db-utils.ts';

interface ChapterPayload {
  englishTitle: string;
  englishTitleTranslation: string;
  index: number;
  title: string;
  verses: number;
}
interface VersePayload {
  chapter: number;
  hizbQuarter: number;
  index: number;
  indexInQuran: number;
  juz: number;
  manzil: number;
  ruku: number;
  text: string;
}
interface PagePayload {
  index: number;
  verseDetails: Record<number, {}>;
}

type StoreName = 'chapters' | 'verses' | 'pages';

const DB_VERSION = 2;
const DB_NAME = 'QuranDatabase';
const processStore = useProcessStore();

const TOTAL_CHAPTERS = 114;
const TOTAL_VERSSES = 6236;

function objectStore(type: StoreName): IDBObjectStore {
  return DbUtils.objectStore(QuranDbService.db, type);
}

function chaptersObjectStore() {
  return objectStore('chapters');
}
function versesObjectStore() {
  return objectStore('verses');
}
function pagesObjectStore() {
  return objectStore('pages');
}

async function fetchFromAPI<T>(endpoint: string, sortFn: (a: T, b: T) => number): Promise<T[]> {
  return DbUtils.fetchFromAPI<T>('quran', endpoint, sortFn);
}

async function fetchChaptersFromAPI(): Promise<any[]> {
  return fetchFromAPI<ChapterPayload>('chapters', (a, b) => a.index - b.index);
}

async function fetchVersesFromAPI(): Promise<any[]> {
  return fetchFromAPI<VersePayload>('verses', (a, b) => a.index - b.index);
}

async function fetchPagesFromAPI(): Promise<any[]> {
  return fetchFromAPI<PagePayload>('pages', (a, b) => a.index - b.index);
}

async function populateChapters() {
  await DbUtils.populateStore({
    expectedCount: TOTAL_CHAPTERS,
    fetchFromAPI: fetchChaptersFromAPI,
    objectStore: chaptersObjectStore,
    storeInDBTransform: record => ({ ...record, id: record.index }),
    storeInMemory: (chapters: Chapter[]) => useChapterStore().populate(chapters),
    transformRecord: record => new Chapter(record),
    writeChunkSize: 57,
  });
}

async function populateVerses() {
  await DbUtils.populateStore({
    expectedCount: TOTAL_VERSSES,
    fetchFromAPI: fetchVersesFromAPI,
    idleProcessor: createVersesInIdle,
    objectStore: versesObjectStore,
    storeInDBTransform: record => ({ ...record, id: record.indexInQuran }),
    storeInMemory: verses => useVerseStore().populate(verses),
  });
}

async function populatePages() {
  await DbUtils.populateStore({
    expectedCount: TOTAL_PAGES,
    fetchFromAPI: fetchPagesFromAPI,
    idleProcessor: createPagesInIdle,
    objectStore: pagesObjectStore,
    storeInDBTransform: record => ({ ...record, id: record.index }),
    storeInMemory: pages => usePageStore().populate(pages),
  });
}

function createPagesInIdle(pagesData: PagePayload[], onComplete: (pages: Page[]) => void) {
  DbUtils.createRecordsInIdle(pagesData, (record: PagePayload) => new Page(record), onComplete);
}

function createVersesInIdle(versesData: VersePayload[], onComplete: (verses: Verse[]) => void) {
  DbUtils.createRecordsInIdle(
    versesData,
    (record: VersePayload) => new Verse({
      ...record,
      chapterIndex: record.chapter,
    }),
    onComplete,
  );
}

export default class QuranDbService extends TavoosService {
  private static _pageDataBeingFetched = new Set<number>();

  static db: IDBDatabase;

  static async inflate() {
    const objectStoreNames = ['chapters', 'verses', 'pages'];
    const result = await DbUtils.openDB(DB_NAME, DB_VERSION, objectStoreNames);

    if (!result) return;

    QuranDbService.db = result;

    await populateChapters();
    await populateVerses();
    await populatePages();
  }

  static isDataLoaded(): boolean {
    const data = [
      { records: useChapterStore().chapters, expectedLength: TOTAL_CHAPTERS },
      { records: useVerseStore().verses, expectedLength: TOTAL_VERSSES },
      { records: usePageStore().pages, expectedLength: TOTAL_PAGES },
    ];

    return data.every(({ records, expectedLength }) => records.length === expectedLength);
  }

  static async populateStores() {
    await populateChapters();
    await populateVerses();
    await populatePages();
  }

  static async fetchPageDetails(number: number) {
    if (QuranDbService._pageDataBeingFetched.has(number)) return;

    QuranDbService._pageDataBeingFetched.add(number);
    console.log(`Fetching data for page ${number}`);

    const pagesEndpoint = `${new Api().serviceURL('quran')}/pages`;

    try {
      const response = await fetch(`${pagesEndpoint}/${number}`);
      const payload = (await response.json()) as PagePayload;
      createPagesInIdle([payload], pages => usePageStore().insert(pages[0]));
      pagesObjectStore().put({ ...payload, id: payload.index });
    } catch (exc) {
      console.error(`Error fetching page ${number} from API:`, exc);
      return;
    } finally {
      QuranDbService._pageDataBeingFetched.delete(number);
    }
  }
}
