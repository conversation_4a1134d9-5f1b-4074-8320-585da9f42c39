<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, watchEffect } from 'vue';
import { RouteLocationNormalized, onBeforeRouteLeave, onBeforeRouteUpdate, useRoute, useRouter } from 'vue-router';

// Pinia Stores
import { usePageStore } from '@/stores/quran/page-store.ts';

// Database Service
import QuranDbService from '@/services/db/quran/quran-db-service.ts';

// Services
import Api from '@/services/api/api.ts';
import Reciter from '@/services/quran/reciter-service.ts';

// Components
import ChaptersListComponent from '@/components/quran/ChaptersListComponent.vue';
import IconButton from '@/components/ui/IconButton.vue';
import LandscapeNavigationComponent from '@/components/quran/LandscapeNavigationComponent.vue';
import MemoryGame from '@/components/quran/MemoryGame.vue';
import Modal from '@/components/ui/ModalComponent.vue';
import PortraitNavigationComponent from '@/components/quran/PortraitNavigationComponent.vue';
import QuranPageComponent from '@/components/quran/QuranPageComponent.vue';
import QuranReciterComponent from '@/components/quran/QuranReciterComponent.vue';
import QuranReciterSettingsComponent from '@/components/quran/QuranReciterSettingsComponent.vue';
import SearchComponent from '@/components/quran/SearchComponent.vue';

// Icons
import BrainIcon from '@/components/icons/BrainIcon.vue';
import ClickIcon from '@/components/icons/ClickIcon.vue';
import SearchIcon from '@/components/icons/SearchIcon.vue';
import Chapter from '@/models/quran/Chapter.ts';
import Verse from '@/models/quran/Verse.ts';

type Direction = 'backward' | 'forward';

// Pinia Stores
const pageStore = usePageStore();

const reciter = Reciter.instance;

// Modal refs
const chaptersListModal = ref<typeof Modal>();
const memoryGameModal = ref<typeof Modal>();
const reciterSettingsModal = ref<typeof Modal>();
const searchModal = ref<typeof Modal>();

// Other template refs
const pageTools = ref<HTMLElement>();
const bookmarksListDialog = ref<HTMLDialogElement>();

// Other refs
const route = useRoute();
const router = useRouter();

const defaultPage = 221;
const pageNumber = ref(defaultPage);
const bookmarks = ref<Record<number, string>>({});

const isDualPageMode = ref(false);
const isFetchingFirstPageOfChapter = ref(false);
const isNavigating = ref(true);

const hasEnoughHorizontalEmptySpace = ref(false);
const selectedChapter = ref<Chapter>();
const isLoadingQuranData = ref(false);

const highlightedVerseIndex = ref(0);

const step = computed(() => (isDualPageMode.value ? 2 : 1));

const rightPageNumber = computed(() => {
  return isDualPageMode.value ? pageNumber.value - 1 + (pageNumber.value % 2) : pageNumber.value;
});

const leftPageNumber = computed(() => +rightPageNumber.value + 1);
const isSinglePageMode = computed(() => !isDualPageMode.value);

const leftPage = computed(() => pageStore.pages.find(page => page.index === leftPageNumber.value));
const rightPage = computed(() => pageStore.pages.find(page => page.index === rightPageNumber.value));

watch(reciter.currentVerse, () => {
  const verseBeingRecited = reciter.currentVerse.value!;

  const onTheSamePage = verseCollection.value.includes(verseBeingRecited);

  if (!onTheSamePage) {
    navigateToPage(verseBeingRecited.page!.index);
  }
});

const verseCollection = computed<Verse[]>(() => {
  const rightPageVerses = (rightPage?.value?.verses ?? []) as Verse[];
  const leftPageVerses = (leftPage?.value?.verses ?? []) as Verse[];

  return isDualPageMode.value
    ? [...rightPageVerses, ...leftPageVerses]
    : rightPageVerses;
});

watchEffect(() => (reciter.collection = verseCollection.value));

function getPhotoContainers() {
  return Array.from(document.querySelectorAll('.quran-page-photo-container')) as HTMLDivElement[];
}

function updateIsDualPageMode() {
  const ys = getPhotoContainers().map(container => container.getBoundingClientRect().y);
  isDualPageMode.value = new Set(ys).size === 1;

  if (isDualPageMode.value) calculateEmptyHorizontalSpace();
}

function calculateEmptyHorizontalSpace() {
  const webpage = document.querySelector('#quran-route-root') as HTMLDivElement;
  const quranPage = getPhotoContainers()[0];

  const webpageWidth = webpage.getBoundingClientRect().width;
  const quranPageWidth = quranPage.getBoundingClientRect().width;

  const emptySpaceOnEachSide = (webpageWidth - 2 * quranPageWidth) / 2;
  hasEnoughHorizontalEmptySpace.value = emptySpaceOnEachSide > 80;
}

function navigate(direction: Direction) {
  let value = pageNumber.value;

  switch (direction) {
    case 'forward':
      value += step.value;
      break;
    case 'backward':
      value -= step.value;
      break;
    default:
      throw `Invalid direction ${direction}`;
  }

  navigateToPage(value);

  // localStorage.setItem('pageNumber', String(pageNumber.value));
}

function toggleBookmark() {
  const page = pageNumber.value;
  if (bookmarks.value[page] === undefined) {
    bookmarks.value = { ...bookmarks.value, [page]: '' };
  } else {
    const obj: Record<number, string> = { ...bookmarks.value };
    delete obj[page];

    bookmarks.value = obj;
  }

  saveBookmarks();
}

function openBookmarksList() {
  bookmarksListDialog.value?.showModal();
}

function closeBookmarksList() {
  bookmarksListDialog.value?.close();
}

function navigateToPage(page: number) {
  closeBookmarksList();

  const value = Math.max(1, Math.min(604, page));
  storePageNumberLocally(value);

  router.push({
    name: route.name!,
    params: {
      ...route.params,
      pageNumber: value,
    },
    query: route.query,
  });
}

function setLabelForBookmark(page: number, event: Event) {
  const input = event.currentTarget as HTMLInputElement;
  const label = input.value;

  const value = { ...bookmarks.value };
  value[page] = label;

  bookmarks.value = value;
  saveBookmarks();
}

function saveBookmarks() {
  localStorage.setItem('bookmarks', JSON.stringify(bookmarks.value));
}

function onPageNumberChange(value: number) {
  navigateToPage(value);
}

function storePageNumberLocally(number: number | string | string[]) {
  localStorage.setItem('pageNumber', String(number));
}

function getLocallyStoredPageNumber() {
  return JSON.parse(`${parseInt(localStorage.getItem('pageNumber') ?? '0') || defaultPage}`) as number;
}

function restoreLocalPageNumber() {
  pageNumber.value = getLocallyStoredPageNumber();
}

function closeChaptersList() {
  chaptersListModal.value?.close();
}

function openChaptersList(chapter?: Chapter | undefined) {
  selectedChapter.value = chapter;

  chaptersListModal.value!.open();
}

async function navigateToChapter(chapter: Chapter) {
  isFetchingFirstPageOfChapter.value = true;

  const pageOfEndpoint = `${new Api().serviceURL('quran')}/page-of/${chapter.index}/1`;

  try {
    const response = await fetch(pageOfEndpoint);

    closeChaptersList();

    navigateToPage(await response.json());
  } catch (exc) {
    console.error({ exc });
  } finally {
    isFetchingFirstPageOfChapter.value = false;
  }
}

function repositionPageTools(mode: 'active' | 'inactive' = 'active') {
  const tools = pageTools.value as HTMLElement;
  const toolsHome = document.querySelector('#page-specific-tools') as HTMLDivElement;

  switch (mode) {
    case 'active':
      while (toolsHome.firstChild) toolsHome.removeChild(toolsHome.firstChild);
      toolsHome.appendChild(tools);
      return;
    case 'inactive':
      if (toolsHome.contains(tools)) toolsHome.removeChild(tools);
      return;
    default:
      throw `unsupported mode: ${mode}`;
  }
}

function switchGlobalEventListeners(state: 'on' | 'off') {
  switch (state) {
    case 'on':
      window.addEventListener('orientationchange', updateIsDualPageMode);
      window.addEventListener('resize', updateIsDualPageMode);
      return;
    case 'off':
      window.removeEventListener('orientationchange', updateIsDualPageMode);
      window.removeEventListener('resize', updateIsDualPageMode);
      return;
    default:
      throw `unsupported mdoe ${state}`;
  }
}

function switchNavigationMode() {
  isNavigating.value = !isNavigating.value;
}

function openMemoryGameModal() {
  memoryGameModal.value?.open();
}

function closeMemoryGameModal() {
  memoryGameModal.value?.close();
}

function openReciterSettingsModal() {
  reciterSettingsModal.value?.open();
}

function switchSearchModal() {
  searchModal.value?.open();
}

async function loadQuranData() {
  // Check if data is already loaded in stores
  if (QuranDbService.isDataLoaded()) {
    await QuranDbService.populateStores();
    return;
  }

  isLoadingQuranData.value = true;

  try {
    // Initialize the database and load data
    QuranDbService.inflate();
  } catch (exc) {
    console.error('Error loading Quran data:', exc);
  } finally {
    // Set loading to false after a short delay to ensure data is populated
    setTimeout(() => isLoadingQuranData.value = false, 1000);
  }
}

function closeSearchModal() {
  searchModal.value?.close();
}

function onKeyDown(event: KeyboardEvent) {
  const modals = [chaptersListModal, memoryGameModal, searchModal];
  const someModalIsOpen = modals.some(modal => modal.value?.isOpen);

  if (someModalIsOpen) {
    if (event.key === 'ArrowDown') {
      closeBookmarksList();
      closeChaptersList();
      closeMemoryGameModal();
      closeSearchModal();
    }
    return;
  }

  switch (event.key) {
    case 'ArrowRight':
      navigate('backward');
      break;
    case 'ArrowLeft':
      navigate('forward');
      break;
    case 'ArrowUp': {
      const verse = rightPage.value?.verses?.[0] as Verse | undefined;
      openChaptersList(verse?.chapter);
      break;
    }
    default:
      break;
  }
}

onBeforeRouteUpdate((to: RouteLocationNormalized) => {
  if (Array.isArray(to.params.pageNumber) || isNaN(parseInt(to.params.pageNumber))) {
    throw new Error('page number must be an integer');
  }

  storePageNumberLocally(to.params.pageNumber);
  restoreLocalPageNumber();
  highlightedVerseIndex.value = to.query.highlight ? parseInt(to.query.highlight as string) : 0;
});

onMounted(() => {
  updateIsDualPageMode();
  repositionPageTools();
  switchGlobalEventListeners('on');

  bookmarks.value = JSON.parse(localStorage.getItem('bookmarks') ?? '{}') as Record<number, string>;
  bookmarksListDialog.value?.close();

  if (route.params.pageNumber) {
    storePageNumberLocally(route.params.pageNumber);
    restoreLocalPageNumber();
  } else {
    navigateToPage(getLocallyStoredPageNumber());
  }

  highlightedVerseIndex.value = route.query.highlight ? parseInt(route.query.highlight as string) : 0;

  window.addEventListener('keydown', onKeyDown);

  // Load Quran data in the background (non-blocking)
  loadQuranData();
});

onBeforeRouteLeave(() => {
  repositionPageTools('inactive');
});

onUnmounted(() => {
  switchGlobalEventListeners('off');
});
</script>

<template>
  <div
    class="contents"
    ref="pageTools"
  >
    <IconButton
      v-if="isSinglePageMode || !hasEnoughHorizontalEmptySpace"
      @click.stop="switchNavigationMode"
      :dataToggle="isNavigating ? 'active' : 'pending'"
      class="max-w-12"
    >
      <ClickIcon :strokeWidth="isNavigating ? 'thick' : 'normal'" />
    </IconButton>

    <IconButton
      v-if="pageStore.pages.length"
      @click.stop="switchSearchModal"
      class="max-w-12"
    >
      <SearchIcon />
    </IconButton>

    <QuranReciterComponent
      class="portrait:h-full"
      @reciter-settings-request="openReciterSettingsModal"
    />

    <IconButton
      @click.stop="openMemoryGameModal"
      class="max-w-12"
    >
      <BrainIcon :strokeWidth="isNavigating ? 'thick' : 'normal'" />
    </IconButton>
  </div>

  <div
    class="h-full overflow-hidden relative"
    id="quran-route-root"
  >
    <div
      id="pages-container"
      class="h-full flex flex-wrap justify-center items-start rtl primary dark:secondary"
      @click.stop="switchNavigationMode"
    >
      <QuranPageComponent
        :pageIndex="rightPageNumber"
        :highlightedVerseIndex="highlightedVerseIndex"
      />
      <QuranPageComponent
        :pageIndex="leftPageNumber"
        :highlightedVerseIndex="highlightedVerseIndex"
        class="portrait:hidden"
      />
    </div>

    <button
      @click.stop="isNavigating = false"
      class="w-16 aspect-square absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2 rounded-full secondary navigation-close-button transition-all ease-in-out duration-700"
      :class="{
        'opacity-0 pointer-events-none scale-50 idle': !isNavigating,
        hidden: isSinglePageMode || hasEnoughHorizontalEmptySpace,
      }"
      title="Click to hide navigation tools"
    ></button>

    <PortraitNavigationComponent
      v-if="isSinglePageMode"
      @change="onPageNumberChange"
      @navigate="navigate"
      @openChaptersList="openChaptersList"
      class="h-12 transition-all duration-300 sticky bottom-0 m-auto z-20"
      :class="[isNavigating ? '-translate-y-2' : 'translate-y-full opacity-0']"
      :pageNumber="rightPageNumber"
      :number="pageNumber"
    />

    <LandscapeNavigationComponent
      v-if="isDualPageMode"
      @change="onPageNumberChange"
      @navigate="navigate"
      @openChaptersList="openChaptersList"
      :keepNavigationArrowsVisible="hasEnoughHorizontalEmptySpace"
      :keepPageNumbersVisible="hasEnoughHorizontalEmptySpace"
      :leftPageNumber="leftPageNumber"
      :rightPageNumber="rightPageNumber"
      :reveal="isNavigating"
    />

    <!-- <dialog
      ref="bookmarksListDialog"
      class="secondary md:w-2/3 md:gap-16 md:p-16 open:flex"
    >
      <div class="w-full flex space-between">
        <h1 class="flex-1 text-4xl font-bold">Bookmarks</h1>
        <button @click.stop="closeBookmarksList">
          <CrossCircularFilledIcon class="h-10 hover:drop-shadow-2xl rounded-full" />
        </button>
      </div>

      <ul class="flex-1 w-full overflow-auto border-t-2">
        <li
          v-for="[page, label] in Object.entries(bookmarks)"
          :key="page"
          class="w-full p-2 md:p-4 flex items-center justify-between gap-4"
          @click="navigateToPage(parseInt(page))"
        >
          <span class="font-bold md:text-2xl">{{ page }}</span>
          <input
            type="text"
            :value="label"
            placeholder="Set a label"
            class="bg-black/0 font-light hover:border-2 hover:bg-teal-600 hover:text-white hover:font-bold p-2 rounded text-right max-w-40 md:max-w-none"
            @click.stop="() => {}"
            @input.stop="(e: Event) => setLabelForBookmark(parseInt(page), e)"
          />
        </li>
      </ul>
    </dialog> -->

    <Modal
      ref="chaptersListModal"
      title="سیاههٔ سوره‌ها"
      class="farsi"
    >
      <ChaptersListComponent
        :isBusy="isFetchingFirstPageOfChapter"
        :selectedChapter="selectedChapter"
        @select="navigateToChapter"
      />
    </Modal>

    <Modal
      ref="memoryGameModal"
      title="آزمون حفظ"
    >
      <MemoryGame :pageIndex="rightPageNumber" />
    </Modal>

    <Modal
      ref="searchModal"
      title="جستجو"
    >
      <SearchComponent @search-result-clicked="closeSearchModal" />
    </Modal>

    <Modal
      ref="reciterSettingsModal"
      title="تنظیمات تلاوت"
    >
      <QuranReciterSettingsComponent />
    </Modal>
  </div>
</template>

<style scoped>
.navigation-close-button::before,
.navigation-close-button::after {
  content: '';
  width: 0.25rem;
  height: 60%;
  border-radius: 0.25rem;
  position: absolute;
  top: 50%;
  left: 50%;
  background-color: white;
  transition: all 1s ease;
}

.navigation-close-button::before {
  transform: translate(-50%, -50%) rotate(-45deg);
}
.navigation-close-button::after {
  transform: translate(-50%, -50%) rotate(45deg);
}

.navigation-close-button.idle::before {
  transform: translate(-50%, -50%) rotate(45deg) scale(50%);
}
.navigation-close-button.idle::after {
  transform: translate(-50%, -50%) rotate(-45deg) scale(50%);
}
.page-photo {
  @apply flex-1 h-full bg-contain bg-no-repeat;
}
</style>
