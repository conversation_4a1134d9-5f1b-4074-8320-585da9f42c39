<template>
  <div class="secondary p-8 mt-8 lg:mt-48 rounded-lg h-full max-h-96 max-w-2xl m-auto flex flex-col gap-4">
    <h1 class="text-6xl font-normal leading mb-12">Social Media</h1>
    <p class="flex-1">Follow Tavoos for latest content and much more on social networks.</p>

    <hr />

    <div class="flex justify-center gap-4">
      <a
        class="primary hover:secondary"
        href="https://t.me/tavoos_channel"
        rel="noreferrer"
        target="_blank"
        title="Tavoos on Telegram"
      >
        Telegram
      </a>
    </div>
  </div>
</template>

<style scoped>
@media (min-width: 1024px) {
  .about {
    min-height: 100vh;
    display: flex;
    align-items: center;
  }
}

a {
  @apply rounded-lg w-36 h-16 p-4 border-2 border-white/0 hover:border-white font-black text-center;
}
</style>
