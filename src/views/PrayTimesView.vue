<script setup lang="ts">
import CitySelector                 from '@/components/praytimes/CitySelector.vue';
import PrayTimesDBService           from '@/services/db/prayTimesDbService.ts';
import TimeTable                    from '@/components/praytimes/TimeTable.vue';
import PrayTimesGraph               from '@/components/praytimes/PrayTimesGraph.vue';
import RamadanRadio                 from '@/components/praytimes/RamadanRadio.vue';
import type { PrayersSchedule }     from '@/types.ts';
import { computed, onMounted, ref } from 'vue';
import { usePrayersScheduleStore }  from '@/stores/pray-times/PrayersScheduleStore.ts';

const citySelector = ref(null);
const currentCity = computed(() => (citySelector.value as any)?.currentCity);
const source = computed(() => {
  const sources = {
    hamburgIslamicCenter: {
      name: 'Hamburg Islamic Center',
      url: 'https://en.izhamburg.com/prayer-times/',
    },
    universityOfTehran: {
      name: 'University of Tehran',
      url: 'https://calendar.ut.ac.ir/Fa/',
    },
    untrusted: {
      name: 'Untrusted',
      url: null,
    },
  };

  switch (currentCity.value.country.continent.code) {
    case 'EU':
      return sources.hamburgIslamicCenter;
    case 'AS':
      switch (currentCity.value.country.name) {
        case 'Iran':
          return sources.universityOfTehran;
        default:
          return sources.untrusted;
      }
    default:
      return sources.untrusted;
  }
});

const isShowingGraph = ref(false);
const eidFetr = new Date(2025, 3, 1); // Month is zero-based, so 3 = April
const isRamadan = new Date() < eidFetr;

const sameDate = (date1: Date, date2: Date): Boolean =>
  date1.getFullYear() === date2.getFullYear()
  && date1.getMonth() === date2.getMonth()
  && date1.getDate() === date2.getDate();

const prayersScheduleStore = usePrayersScheduleStore();
const scheduleDate = ref(new Date());
const schedule = computed((): PrayersSchedule | undefined => {
  return prayersScheduleStore.schedules
    .filter((schedule: PrayersSchedule) => schedule.city === currentCity.value)
    .find((schedule: PrayersSchedule) => sameDate(schedule.date, scheduleDate.value));
});

function onDateSelect(date: Date) {
  scheduleDate.value = date;
}

function showGraph() {
  isShowingGraph.value = true;
}

function initializePrayTimes() {
  PrayTimesDBService.inflate();
}

onMounted(initializePrayTimes);
</script>

<template>
  <div class="w-full h-full overflow-hidden flex items-center p-1">
    <div class="w-full h-full max-w-lg max-h-160 overflow-hidden m-auto flex flex-col secondary rounded-md">
      <CitySelector ref="citySelector" />

      <TimeTable
        v-if="currentCity && !isShowingGraph"
        :date="schedule?.date ?? new Date()"
        :observesSummerTime="currentCity.country.summerTimeMonths.includes(new Date().getMonth() + 1)"
        :source="source"
        :timeTable="schedule?.timeTable"
        @date-select="onDateSelect"
        @show-graph="showGraph"
      />

      <RamadanRadio
        v-if="isRamadan && schedule"
        :schedule="schedule"
      />

      <PrayTimesGraph
        v-if="currentCity && isShowingGraph"
        :city="currentCity"
        @close="isShowingGraph = false"
      />
    </div>
  </div>
</template>

<style scoped></style>
