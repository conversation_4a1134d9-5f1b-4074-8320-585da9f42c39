import Verse from "@/models/quran/Verse.ts";
import { useVerseStore } from '@/stores/quran/verse-store.ts';

export default class VerseUtils {
  static sortIds(ids: string[]) {
    return ids.sort(VerseUtils.compareIds);
  }

  static sortVerses(verses: Verse[]) {
    return verses.sort((v, w) => v.indexInQuran < w.indexInQuran ? -1 : 1);
  }

  static findVerse(indexInQuran: number): Verse | undefined {
    return (useVerseStore().verses as Verse[]).find(verse => verse.indexInQuran === indexInQuran);
  }

  private static compareIds(id1: string, id2: string) {
    const [vChapter, vIndex] = id1.split(':').map(x => parseInt(x, 10));
    const [wChapter, wIndex] = id2.split(':').map(x => parseInt(x, 10));

    switch (true) {
      case vChapter < wChapter:
        return -1;
      case vChapter > wChapter:
        return 1;
      case vIndex === wIndex:
        return 0;
      default:
        return vIndex < wIndex ? -1 : 1;
    }
  }
}
