function devMode() {
  return import.meta.env.MODE === 'development';
}

type SortDirection = 'asc' | 'desc';

function sortBy<T>(property: keyof T, direction: SortDirection = 'asc'): (a: T, b: T) => number {
  const amplitude = direction === 'asc' ? 1 : -1;

  return (item1: T, item2: T) => (item1[property] > item2[property] ? 1 : -1) * amplitude;
}

function runWhenIdle<T>(fn: () => T) {
  if ('requestIdleCallback' in window) {
    return requestIdleCallback(fn);
  }

  return setTimeout(fn, 0);
}

function uniq<T = any>(array: T[]) {
  return array.filter((chapter, index, arr) => arr.indexOf(chapter) === index);
}

function compact<T = any>(array: T[]) {
  return array.filter(Boolean);
}

export { compact, devMode, sortBy, runWhenIdle, uniq };
