import { defineStore } from 'pinia';
import type City from '@/models/City.ts';
import type Country from '@/models/Country.ts';
import PrayTimesDBService from '@/services/db/prayTimesDbService.ts';

export const useCityStore = defineStore('CityStore', {
  state: () => ({
    cities: [] as City[],
  }),

  getters: {
    citiesOfCountry(state) {
      return function (country: Country) {
        const cities = state.cities.filter(city => city.countryId === country.id);

        if (!cities.length) {
          PrayTimesDBService.getCitiesOfCountry(country);
        }

        return cities as City[];
      };
    },
  },

  actions: {
    addCity(city: City) {
      if (this.cities.some(({ id }) => id === city.id)) return;

      this.cities.push(city);
    },
  },
});
