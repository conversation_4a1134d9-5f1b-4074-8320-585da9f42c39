import { defineStore } from 'pinia';
import type City from '@/models/City.ts';
import type { PrayersSchedule } from '@/types.ts';

export const usePrayersScheduleStore = defineStore('PrayersScheduleStore', {
  state: () => ({
    schedules: [] as PrayersSchedule[],
  }),

  getters: {
    scheduleOfCity: state => {
      return (city: City) => state.schedules.find(schedule => schedule.city === city);
    },
  },

  actions: {
    addSchedule(schedule: PrayersSchedule) {
      const existingSchedule = this.schedules.find(({ date, city }) => {
        return date.valueOf() === schedule.date.valueOf() && city.id === schedule.city.id;
      });

      if (existingSchedule) {
        existingSchedule.timeTable = schedule.timeTable;
      } else {
        this.schedules.push(schedule);
      }
    },

    flush() {
      while (this.schedules.length) {
        this.schedules.pop();
      }
    },
  },
});
