import { reactive } from 'vue';
import { defineStore } from 'pinia';
import type Process from '@/models/application/Process.ts';

export const useProcessStore = defineStore('ProcessStore', {
  state: () => ({
    processes: [] as Process[],
  }),

  getters: {
    activeProcesses: state => state.processes.filter(p => p.progress.completionRate < 1),
  },

  actions: {
    addProcess(process: Process) {
      this.processes.push(reactive(process));
    },
  },
});
