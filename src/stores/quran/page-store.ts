import Page, { TOTAL_PAGES } from '@/models/quran/Page.ts';
import { defineStore } from 'pinia';

export const usePageStore = defineStore('PageStore', {
  state: () => ({
    pages: [] as Page[],
  }),

  getters: {
    isLoaded: state => state.pages.length == TOTAL_PAGES,
  },

  actions: {
    insert(page: Page) {
      const index = this.pages.findIndex(p => p.index === page.index);

      if (index != -1) {
        this.pages[index] = page;
      } else {
        this.pages.push(page);
      }
    },

    populate(pages: Page[]) {
      this.pages = pages;
    },

    clear() {
      this.pages = [];
    },
  },
});
