import Chapter from '@/models/quran/Chapter.ts';
import { defineStore } from 'pinia';

export const useChapterStore = defineStore('ChapterStore', {
  state: () => ({
    chapters: [] as Chapter[],
  }),

  getters: {
    isLoaded: state => state.chapters.length > 0,
  },

  actions: {
    populate(chapters: Chapter[]) {
      this.chapters = chapters;
    },

    clear() {
      this.chapters = [];
    },
  },
});
