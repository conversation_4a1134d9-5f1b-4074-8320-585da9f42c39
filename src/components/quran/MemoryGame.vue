<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import NumberUtils from '@/utils/number-utils.ts';
import { usePageStore } from '@/stores/quran/page-store.ts';
import Page from '@/models/quran/Page.ts';
import Verse from '@/models/quran/Verse.ts';

interface Challenge {
  verse: Verse;
  answers: number[];
  correctAnswer: number;
}

const props = defineProps<{ pageIndex: number }>();

const isEvaluating = ref(false);
const wasCorrect = ref(false);
const chosenAnswer = ref(-1);
const challenges = ref<Challenge[]>([]);

const currentChallenge = computed(() => [...challenges.value].pop());

const page = computed(() => (usePageStore().pages as Page[]).find(page => page.index === props.pageIndex));

function shuffled<T>(array: T[]) {
  const clone = [...array];
  const result: T[] = [];

  while (clone.length) {
    const randomIndex = Math.floor(Math.random() * clone.length);
    const [randomItem] = clone.splice(randomIndex, 1);
    result.push(randomItem);
  }

  return result;
}

function draw() {
  if (!page.value) return;

  isEvaluating.value = false;

  const index = Math.floor(Math.random() * (page.value.verses.length ?? 0));

  const verse = page.value.verses[index];
  const correctAnswer = verse?.index;

  const availableIndices = [...new Set((page.value.verses as Verse[]).map((verse: Verse) => verse.index))].sort();
  const wrongIndices = availableIndices.filter(index => index !== correctAnswer);
  const wrongAnswers = shuffled(wrongIndices).splice(0, 3);

  const answers = shuffled([...wrongAnswers, correctAnswer]);

  challenges.value.push({
    verse,
    answers,
    correctAnswer,
  });
}

function evaluate(event: MouseEvent) {
  event.stopPropagation();

  isEvaluating.value = true;

  const btn = event.currentTarget as HTMLButtonElement;

  chosenAnswer.value = parseInt(btn.getAttribute('data-answer') ?? '-1');
  wasCorrect.value = chosenAnswer.value === currentChallenge.value?.correctAnswer;
}

onMounted(() => {
  draw();
});
</script>

<template>
  <div
    v-if="currentChallenge"
    class="h-full flex flex-col"
  >
    <p class="p-4 md:p-8 quran arabic flex-1 overflow-auto">
      {{ currentChallenge.verse.text }}
    </p>

    <div
      ref="progressbar"
      class="h-3"
    >
      <div
        id="memory-game-progress-bar"
        v-if="isEvaluating"
        @animationend="draw"
        class="h-full bg-white"
      />
    </div>

    <fieldset
      class="grid grid-cols-4 gap-2 md:gap-4 rtl p-4 md:p-8 bg-black/25"
      :class="{ 'pointer-events-none': isEvaluating }"
    >
      <button
        v-for="(answer, index) in currentChallenge.answers"
        :key="`challenge-verse-${currentChallenge.verse.indexInQuran}-${index}-${answer}`"
        class="border-2 rounded-lg p-2 text-xl"
        :class="{
          'bg-emerald-500 hover:bg-emerald-500': isEvaluating && answer == currentChallenge.correctAnswer,
          'bg-red-500     hover:bg-red-500':     isEvaluating && !wasCorrect && answer == chosenAnswer,
          'hover:bg-unset': isEvaluating,
        }"
        @click="evaluate"
        :data-answer="answer"
      >
        {{ NumberUtils.persianize(answer) }}
      </button>
    </fieldset>
  </div>
</template>

<style scoped>
#memory-game-progress-bar {
  transform-origin: left;
  animation: grow 2s linear;
}

@keyframes grow {
  from {
    transform: scaleX(0);
  }

  to {
    transform: scaleX(1);
  }
}
</style>
