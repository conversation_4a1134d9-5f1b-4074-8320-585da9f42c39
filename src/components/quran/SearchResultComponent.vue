<script setup lang="ts">
// Utilities
import NumberUtils from '@/utils/number-utils.ts';
import { SearchResult, SearchType } from './SearchComponent.vue';
import { computed, nextTick, ref, watch } from 'vue';

const props = defineProps<{
  result: SearchResult;
  searchType: SearchType;
  seed: number;
}>();

const row = ref<HTMLDivElement | null>(null);

const decoratedResult = computed(() => {
  return props.searchType === 'fuzzy' ? decorateFuzzy() : decorateContainment();
});

function decorateContainment(): string {
  const result: SearchResult = props.result;
  const words = result.verse.text.split(/\s+/);
  const purifiedWords = result.verse.purifiedText.split(/\s+/);
  const matches = result.matches.flat();

  return words
    .map((word, index) => {
      const isMatch = matches.some(match => purifiedWords[index].includes(match));
      return isMatch ? `<span class="text-highlight">${word}</span>` : word;
    })
    .join(' ');
}

// For now, they are identical
function decorateFuzzy(): string {
  const result: SearchResult = props.result;
  const words = result.verse.text.split(/\s+/);
  const purifiedWords = result.verse.purifiedText.split(/\s+/);
  const matches = result.matches.flat();

  return words
    .map((word, index) => {
      return matches.some(match => purifiedWords[index].includes(match))
        ? `<span class="text-highlight">${word}</span>`
        : word;
    })
    .join(' ');
}

watch([decoratedResult, props], async () => {
  await nextTick();
  scrollToFirstHighlight();
});

function scrollToFirstHighlight() {
  const rowEl = row.value;
  if (!rowEl) return;

  const highlight = rowEl.querySelector('.text-highlight') as HTMLElement;
  if (!highlight) return;

  highlight.scrollIntoView({
    behavior: 'smooth',
    inline: 'center',
    block: 'nearest',
  });
}
</script>

<template>
  <div
    ref="row"
    class="flex-1 text-xl whitespace-nowrap overflow-hidden"
    style="line-height: normal; font-family: me-quran"
  >
    <div
      ref="inner"
      v-html="decoratedResult"
    />
  </div>

  <div
    class="min-w-24 h-fit flex items-center gap-2 justify-between primary rounded-full px-3"
    style="line-height: normal"
  >
    <span class="whitespace-nowrap">{{ result.verse.chapter.title }}</span>
    <span>{{ NumberUtils.persianize(result.verse.index) }}</span>
  </div>
</template>
