<script setup lang="ts">
import { computed, ref } from 'vue';

// Types
import { Point } from '@/types/types-quran.ts';
import { Side } from '@/types/types-general.ts';

// Services
import ClipboardService from '@/services/general/clipboard-service.ts';
import Reciter from '@/services/quran/reciter-service.ts';

// Icons
import AcademyCapIcon from '@/components/icons/AcademyCapIcon.vue';
import ClipboardCheckmarkIcon from '@/components/icons/ClipboardCheckmarkIcon.vue';
import ClipboardPlusIcon from '@/components/icons/ClipboardPlusIcon.vue';
import MediaPauseIcon from '@/components/icons/MediaPauseIcon.vue';
import MediaPlayIcon from '@/components/icons/MediaPlayIcon.vue';
import StickyNoteIcon from '@/components/icons/StickyNoteIcon.vue';
import TranslationIcon from '@/components/icons/TranslationIcon.vue';

// Components
import CircularProgressbar from '@/components/ui/CircularProgressbar.vue';
import VerseEndSignMenuItemComponent, { VerseEndSignMenuItem } from '@/components/quran/VerseEndSignMenuItem.vue';
import { useVerseStore } from '@/stores/quran/verse-store.ts';
import Verse from '@/models/quran/Verse.ts';

const props = defineProps<{
  verseIndex: number;
  highlight: boolean;
  coordinates: Point;
}>();

const reciter = Reciter.instance;
const remoteURL = 'https://tavoos.eu/quran/verse';

const side = ref<Side>('left');
const hasJustBeenCopiedToTheClipboard = ref(false);

const isMenuActivated = ref(false);
const isActiveInReciter = computed(() => reciter.isActive.value && verse.value === reciter.currentVerse.value);

const verse = computed(() => (useVerseStore().verses as Verse[]).find((verse => verse.indexInQuran === props.verseIndex)));
const verseURL = computed(
  () => `https://tavoos.eu/quran/page/${verse.value?.page?.index}?highlight=${verse.value?.indexInQuran}`,
);

const menuItems = computed(() => {
  const isBeingRecited = reciter.currentVerse.value === verse.value && reciter.isPlaying.value;

  const items = [
    {
      icon: TranslationIcon,
      index: 0,
      title: 'ترجمه',
      action: () => navigateTo('translations'),
    },
    {
      icon: StickyNoteIcon,
      index: 1,
      title: 'یادداشت‌ها',
      action: () => navigateTo('notes/list'),
    },
    {
      icon: AcademyCapIcon,
      index: 2,
      title: 'تفسیر',
      action: () => navigateTo('studies'),
    },
    {
      icon: isBeingRecited ? MediaPauseIcon : MediaPlayIcon,
      index: 3,
      title: isBeingRecited ? 'توقف' : 'پخش',
      action: () => reciter.playOrPause(verse.value),
    },
    {
      icon: hasJustBeenCopiedToTheClipboard.value ? ClipboardCheckmarkIcon : ClipboardPlusIcon,
      index: 4,
      title: 'نسخه‌برداری',
      action: () => copyToClipboard(),
      disabled: hasJustBeenCopiedToTheClipboard.value,
    },
  ];

  return items as VerseEndSignMenuItem[];
});

function onClick(event: MouseEvent) {
  const element = event.currentTarget as HTMLElement;
  const parent = element.parentElement!;

  const x = element.offsetLeft;
  const y = element.offsetTop;

  const rightOrLeft: Side = x < parent.clientWidth / 2 ? 'right' : 'left';

  const heightRatio = y / parent?.clientHeight;

  if (1 / 5 < heightRatio && heightRatio < 4 / 5) {
    activateMenu(rightOrLeft);
  } else {
    const topOrBottom = heightRatio >= 4 / 5 ? 'top' : 'bottom';

    activateMenu(`${topOrBottom}-${rightOrLeft}`);
  }
}

function activateMenu(onSide: Side = 'left') {
  isMenuActivated.value = true;

  side.value = onSide;

  setTimeout(() => collapseMenu(), 5000);
}

function collapseMenu() {
  isMenuActivated.value = false;
}

function navigateTo(destination: string) {
  if (!verse.value) return;

  const url = [remoteURL, verse.value.chapter.index, verse.value.index, destination].join('/');

  window.location.href = url;
}

function copyToClipboard() {
  ClipboardService.copy([verse.value?.text, verseURL.value].join('\n'));

  hasJustBeenCopiedToTheClipboard.value = true;

  setTimeout(() => (hasJustBeenCopiedToTheClipboard.value = false), 2000);
}

defineExpose({
  collapseMenu,
  verseIndex: props.verseIndex,
});
</script>

<template>
  <div
    class="absolute transition-opacity w-[5%] aspect-square"
    :style="{
      left: `${100 * (props.coordinates?.x ?? 0)}%`,
      top: `${100 * (props.coordinates?.y ?? 0)}%`,
    }"
    :data-verse-end-sign="props.verseIndex"
    @click.stop="onClick"
    :title="props.verseIndex.toString()"
  >
    <CircularProgressbar
      class="w-full -translate-x-1/2 -translate-y-1/2 cursor-pointer bg-orange-200 dark:bg-orange-900 hover:bg-tavoos-orange"
      :class="{
        'opacity-100 hover:opacity-100 bg-tavoos-orange': isMenuActivated || isActiveInReciter,
        'bg-tavoos-orange': isMenuActivated,
        'ring-4 ring-offset-8 ring-tavoos-orange': props.highlight,
      }"
      :percentage="reciter.currentVerse.value === verse ? Math.floor(100 * reciter.progress.value) : 0"
    />

    <VerseEndSignMenuItemComponent
      v-for="item in menuItems"
      :key="`verse-end-sign-menu-item-at-index-${item.index}`"
      :active="isMenuActivated"
      :item="item"
      :side="side"
      :totalItems="menuItems.length"
      @click.stop="item.action"
      :class="{ 'pointer-events-none': item.disabled }"
    />
  </div>
</template>
