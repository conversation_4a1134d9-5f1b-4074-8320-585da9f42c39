<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { Side } from '@/types/types-general.ts';
import type { Component } from 'vue';

export interface VerseEndSignMenuItem {
  icon: Component;
  index: number;
  title: string;
  action: Function;
  disabled?: boolean;
}

const props = defineProps<{
  active: boolean;
  item: VerseEndSignMenuItem;
  side: Side;
  totalItems: number;
}>();

const transitionDuration = ref(0);

const startAngle = 135;
const endAngle = 225;

const amplitude = computed(() => (props.active ? 1 : 0));
const stepSize = computed(() => (endAngle - startAngle) / (props.totalItems - 1));
const angle = computed(() => startAngle + props.item.index * stepSize.value);

function updateTransitionSpeed() {
  transitionDuration.value = 100 + 500 * Math.random();
}

onMounted(updateTransitionSpeed);
</script>

<template>
  <div
    class="menu-item w-12 absolute cursor-pointer transition-all ease-in bg-tavoos-orange outline outline-2 hover:outline-4 outline-black rounded-full z-20"
    :class="[`${props.side}-side`, active ? 'opacity-100' : 'opacity-0 pointer-events-none']"
    @transitionend="updateTransitionSpeed"
    :style="{
      '--width': '3rem',
      '--ampltitude': amplitude,
      '--angle': `${angle}deg`,
      '--angle-gap-half': `${stepSize / 2}deg`,
      '--index': props.item.index,
      '--radial-gap-amplitude': 1.2,
      '--radius': 'calc((var(--radial-gap-amplitude) * var(--width) / 2) / sin(var(--angle-gap-half)))',
      '--total-items': props.totalItems,
      '--transition-duration': `${transitionDuration}ms`,
      '--vertical-gap': '0.25rem',
      width: 'var(--width)',
    }"
    :index="props.item.index"
    :label="props.item.title"
  >
    <component
      :is="props.item.icon"
      class="w-full aspect-square rounded-full transition-all pointer-events-none text-black bg-none mix-blend-multiply"
    />
  </div>
</template>

<style scoped>
.menu-item {
  &.left-side {
    left: calc(var(--ampltitude) * var(--radius) * cos(var(--angle)));
  }

  &.right-side {
    right: calc(var(--ampltitude) * var(--radius) * cos(var(--angle)));
  }

  &.left-side,
  &.right-side {
    top: calc(var(--ampltitude) * var(--radius) * sin(var(--angle)));
    @apply -translate-y-1/2;
  }

  &.bottom-right-side,
  &.top-right-side {
    left: calc(var(--ampltitude) * 2rem);
  }

  &.bottom-left-side,
  &.top-left-side {
    right: calc(var(--ampltitude) * 3rem);
  }

  &.bottom-right-side,
  &.bottom-left-side {
    --top-offset: calc((var(--total-items) - var(--index) - 1) * (3.5rem + var(--vertical-gap)));

    top: calc(var(--ampltitude) * var(--top-offset));
  }

  &.top-right-side,
  &.top-left-side {
    --bottom-offset: calc(var(--index) * (3.5rem + var(--vertical-gap)) + 2rem);

    bottom: calc(var(--ampltitude) * var(--bottom-offset));
  }

  transition-duration: var(--transition-duration);

  &::after {
    content: attr(label);

    /* Secondary */
    background-color: var(--color-secondary-background-color);
    color: var(--color-secondary-text-color);

    @apply pointer-events-none block whitespace-nowrap px-2 py-1 rounded absolute top-1/2 -translate-y-1/2 transition-all duration-300 ease-out outline outline-1 outline-black;
  }

  &.left-side,
  &.bottom-left-side,
  &.top-left-side {
    &::after {
      @apply right-20 opacity-0;
    }

    &:hover::after {
      @apply right-14 opacity-100;
    }
  }

  &.right-side,
  &.bottom-right-side,
  &.top-right-side {
    &::after {
      @apply left-20 opacity-0;
    }

    &:hover::after {
      @apply left-14 opacity-100;
    }
  }
}
</style>
