<script setup lang="ts">
import PageNumberComponent from '@/components/quran/PageNumberComponent.vue';
import PreviousPageButton from '@/components/quran/PreviousPageButton.vue';
import NextPageButton from '@/components/quran/NextPageButton.vue';
import { computed } from 'vue';

import PopupIcon from '@/components/icons/PopupIcon.vue';
import { usePageStore } from '@/stores/quran/page-store.ts';
import Page from '@/models/quran/Page.ts';

defineEmits(['change', 'navigate', 'openChaptersList']);

const props = defineProps<{
  keepNavigationArrowsVisible: Boolean;
  keepPageNumbersVisible: Boolean;
  leftPageNumber: number;
  reveal: boolean;
  rightPageNumber: number;
}>();

const navigationArrowsVisible = computed(() => props.keepNavigationArrowsVisible || props.reveal);
const pageNumbersVisible = computed(() => props.keepPageNumbersVisible || props.reveal);
const rightPage = computed(() => (usePageStore().pages as Page[]).find(page => page.index === props.rightPageNumber));
const leftPage = computed(() => (usePageStore().pages as Page[]).find(page => page.index === props.leftPageNumber));

function rightNumber(value: number) {
  return value - 1 + (value % 2);
}
</script>

<template>
  <div>
    <!-- Left column -->
    <button
      class="primary dark:secondary rounded pt-1 flex flex-col items-center gap-2 absolute top-2 lg:top-4 left-2 lg:left-4"
      @click.stop="$emit('openChaptersList', leftPage?.chapters[0])"
    >
      <div
        class="arabic text-left text-xl"
        style="writing-mode: vertical-rl; text-orientation: mixed"
      >
        <div>{{ leftPage?.chapters[0]?.title }} | جزء {{ Intl.NumberFormat('fa-IR').format(leftPage?.part ?? 1) }}</div>
      </div>

      <PopupIcon
        class="w-6 text-transparent"
        stroke-color="black"
      />
    </button>

    <NextPageButton
      @click.stop="$emit('navigate', 'forward')"
      class="primary dark:secondary navigation-button left-0"
      :class="[navigationArrowsVisible ? 'translate-x-2' : '-translate-x-full']"
    />

    <PageNumberComponent
      :value="leftPageNumber"
      @change="(value: number) => $emit('change', rightNumber(value))"
      class="page-number primary dark:secondary left-3"
      :class="[pageNumbersVisible ? '-translate-y-6' : 'translate-y-full']"
    />

    <!-- Right column -->
    <button
      class="primary dark:secondary rounded pt-1 flex flex-col items-center gap-2 absolute top-2 lg:top-4 right-2 lg:right-4"
      @click.stop="$emit('openChaptersList', leftPage?.chapters[0])"
    >
      <div
        class="arabic text-left text-xl"
        style="writing-mode: vertical-rl; text-orientation: mixed"
      >
        <div>{{ rightPage?.chapters[0]?.title }} | جزء {{ Intl.NumberFormat('fa-IR').format(rightPage?.part ?? 1) }}</div>
      </div>

      <PopupIcon
        class="w-6 text-transparent"
        stroke-color="black"
      />
    </button>

    <PreviousPageButton
      @click.stop="$emit('navigate', 'backward')"
      class="primary dark:secondary navigation-button right-0"
      :class="[navigationArrowsVisible ? '-translate-x-2' : 'translate-x-full']"
    />

    <PageNumberComponent
      :value="rightPageNumber"
      @change="(value: number) => $emit('change', rightNumber(value))"
      class="page-number primary dark:secondary right-3"
      :class="[pageNumbersVisible ? '-translate-y-6' : 'translate-y-full']"
    />
  </div>

  <!-- Tools -->
  <!-- <div
      class="flex items-center justify-center gap-4 md:gap-8 lg:gap-4 px-2 lg:py-8 portrait:secondary outline outline-1 -outline-offset-1 outline-teal-600 rounded-lg w-fit m-auto"
      style="flex-direction: inherit"
    >
      <div
        class="flex items-center gap-2 md:gap-4 h-2/3"
        style="flex-direction: inherit"
      >
        <a
          :href="`https://tavoos.eu/quran/page/${pageNumber}/text`"
          class="icon"
        >
          <AccordionIcon />
        </a>
      </div>

      <div
        class="flex items-center gap-2 md:gap-4 h-2/3"
        style="flex-direction: inherit"
      >
        <button
          class="icon text-teal-700 primary border-2 border-teal-600 rounded-lg transition-opacity duration-1000"
          @click.stop="openBookmarksList"
          :class="Object.keys(bookmarks).length > 0 ? 'opacity-100' : 'opacity-0 pointer-events-none'"
        >
          <ListStarsIcon />
        </button>

        <button
          class="icon text-teal-700"
          @click.stop="toggleBookmark"
        >
          <StarOutlineIcon v-if="bookmarks[pageNumber] === undefined" />
          <StarFilledIcon v-else />
        </button>
      </div>
    </div> -->
</template>

<style scoped>
.navigation-button {
  aspect-ratio: 1;

  @apply w-8 lg:w-10 absolute top-1/2 -translate-y-1/2 transition-transform;
}

.page-data {
  @apply absolute top-0 text-center transition-transform text-xl;
  color: var(--color-primary-text-color);
}

.page-number {
  @apply absolute bottom-0 text-center transition-transform;
  /* color: var(--color-primary-text-color); */
}
</style>
