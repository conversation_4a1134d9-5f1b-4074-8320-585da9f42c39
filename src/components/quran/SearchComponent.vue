<script setup lang="ts">
// Vue stuff
import { computed, ref, watch } from 'vue';
import { useRouter } from 'vue-router';

// Utilities
import NumberUtils from '@/utils/number-utils.ts';
import StringUtils from '@/utils/string-utils.ts';

// Pinia Stores
import { useVerseStore } from '@/stores/quran/verse-store.ts';

// Components
import SearchResultComponent from '@/components/quran/SearchResultComponent.vue';

// Models
import Verse from '@/models/quran/Verse.ts';

export type SearchType = 'containment' | 'fuzzy' | 'full';

const emit = defineEmits(['search-result-clicked']);

const router = useRouter();

// Pinia Stores
const verseStore = useVerseStore();

const resultsList = ref<HTMLUListElement>();

export interface SearchResult {
  verse: Verse;
  matches: RegExpExecArray[];
}

const numberOfResultsToRender = ref(50);
const searchResults = ref<SearchResult[]>([]);
const searchTerm = ref('');
const searchTimeout = ref<NodeJS.Timeout>();

const searchFuzzy = ref(true);
const seed = ref(0.0);

const searchType = computed(() => searchFuzzy.value ? 'fuzzy' : 'containment' as SearchType);

watch([searchTerm, searchType], ([key, type]) => {
  numberOfResultsToRender.value = 50;

  seed.value = Math.random();

  if (key.trim() === '') return;

  clearTimeout(searchTimeout.value);
  searchTimeout.value = setTimeout(() => searchOffline({ key }), 350);
});

const renderableResults = computed(() => searchResults.value.slice(0, numberOfResultsToRender.value));

function updateList(event: UIEvent) {
  const element = event.currentTarget! as HTMLUListElement;
  const closeToScrollEnd = element.scrollTop + element.offsetHeight >= element.scrollHeight * 0.8;

  numberOfResultsToRender.value += closeToScrollEnd ? 50 : 0;
}

function onSearchResultClick(verse: Verse) {
  const page = verse.page;

  if (!page) {
    console.error('Page not found for Verse', verse);

    return;
  }

  router.push({
    path: `/quran/${page.index}`,
    query: {
      highlight: verse.indexInQuran.toString(),
    },
  });

  emit('search-result-clicked');
}

async function searchOffline({ key }: { key: string }) {
  const verses = verseStore.verses as Verse[];

  const stringUtils = new StringUtils();
  const purifiedKey = key
    .split(/\s+/)
    .map(word => new StringUtils().purify(word))
    .join(' ');
  const zippedPurifiedKey = stringUtils.zip(purifiedKey);

  let regExp: RegExp;
  switch (searchType.value) {
    case 'full':
      regExp = new RegExp(` ${zippedPurifiedKey} `, 'g');
      break;
    case 'containment':
      regExp = new RegExp(purifiedKey, 'g');
      break;
    case 'fuzzy':
      regExp = new RegExp([...purifiedKey].join('\\S*'), 'g');
      break;
    default:
      regExp = new RegExp('', 'g');
      break;
  }

  searchResults.value = verses
    .map(verse => ({
      verse,
      matches: [...verse.purifiedText.matchAll(regExp)],
    }) as SearchResult)
    .filter(result => result.matches.length > 0);
}
</script>

<template>
  <div class="h-full overflow-auto flex flex-col gap-2 p-2 md:p-4">
    <ul
      ref="resultsList"
      class="flex-1 overflow-auto"
      @scroll="updateList"
    >
      <li
        v-for="result in renderableResults"
        :key="`search-result-${result.verse.indexInQuran}`"
        class="px-2 md:ps-4 py-2 flex items-center gap-2 arabic text-start"
        @click.stop="() => onSearchResultClick(result.verse as Verse)"
      >
        <SearchResultComponent :result="result as SearchResult" :search-type="searchType" :seed="seed" />
      </li>
    </ul>

    <label class="flex gap-2 md:gap-4 items-center rtl">
      <input
        type="checkbox"
        v-model="searchFuzzy"
        class="p-1 md:p-4 rounded"
      />
      <!-- <span>Fuzzy</span> -->
      <span class="text-sm md:text-xl">تقریبی</span>
    </label>

    <div class="flex items-center gap-4 px-2 border-t-2 pt-2">
      <div
        class="my-2 farsi shrink-0"
        v-if="searchResults.length"
      >
        {{ NumberUtils.persianize(searchResults.length) }} نتیجه
      </div>

      <div class="flex-1">
        <input
          class="primary px-2 py-3 rounded-lg w-full farsi"
          placeholder="پی چی می‌گردی؟"
          type="text"
          v-model="searchTerm"
        />
      </div>
    </div>
  </div>
</template>
