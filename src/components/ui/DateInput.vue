<script setup lang="ts">
import { computed, ref } from 'vue';

const emit = defineEmits(['change']);

const props = defineProps<{
  date: Date;
  earliest?: Date;
  latest?: Date;
  warning: boolean;
}>();

const locale = 'en-US';

const humanReadable = computed(() => {
  return props.date.toLocaleDateString(locale, {
    weekday: 'short',
    month: 'short',
    day: 'numeric',
    year: 'numeric',
  });
});

const showDatePicker = ref(false);

function machineReadableForm(date?: Date): string {
  if (!date) return '';

  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();

  return `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
}

function onChange(e: Event) {
  showDatePicker.value = false;

  const input = e.currentTarget as HTMLInputElement;

  emit('change', new Date(input.value));
}
</script>

<template>
  <input
    v-if="showDatePicker"
    type="date"
    :value="machineReadableForm($props.date)"
    :min="machineReadableForm($props.earliest)"
    :max="machineReadableForm($props.latest)"
    class="px-2 py-1 border-2 border-black rounded mix-blend-multiply"
    :class="{ hidden: !showDatePicker }"
    @change="onChange"
  />

  <button
    v-else
    @click="showDatePicker = true"
    class="px-2 py-1 border-2 rounded border-black mix-blend-multiply"
    :class="{
      hidden: showDatePicker,
      'bg-yellow-100': $props.warning,
    }"
  >
    {{ humanReadable }}
  </button>
</template>
