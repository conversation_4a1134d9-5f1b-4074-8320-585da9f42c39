<script setup lang="ts">
import { onMounted, ref } from 'vue';

const emit = defineEmits(['filter', 'filterToggle']);

const props = defineProps<{
  initialValue: string;
}>();

const showFilter = ref(false);

function toggleFilter() {
  showFilter.value = !showFilter.value;
  emit('filterToggle', showFilter.value);
  if (showFilter.value) {
    searchField().focus();
  }
}

function setFilter(value: string) {
  searchField().value = value;
  emit('filter', value);
}

function clearFilter() {
  setFilter('');
  if (showFilter.value) toggleFilter();
}

function searchField(): HTMLInputElement {
  return document.querySelector('#location-search-filed') as HTMLInputElement;
}

onMounted(() => setFilter(props.initialValue ?? ''));

defineExpose({ clearFilter });
</script>

<template>
  <div
    class="rounded-r-full secondary h-14 overflow-hidden flex items-center justify-between content-end transition-all ease cursor-pointer p-1 drop-shadow-xl border-2 border-white"
    :class="{ 'w-14 rounded-l-full': !showFilter }"
    :style="{ width: showFilter ? 'calc(100% - 1.5rem)' : '' }"
  >
    <div
      class="h-full transition-opacity"
      style="width: calc(100% - 3.5rem)"
      :style="{ opacity: showFilter ? 1 : 0 }"
    >
      <input
        @input="(e: Event) => setFilter((e.target as HTMLInputElement).value)"
        class="primary px-2 h-full w-full"
        id="location-search-filed"
        type="text"
      />
    </div>

    <div
      class="h-12 w-12 rounded-full transition-opacity ease relative"
      :class="[showFilter ? 'bg-black/25' : 'bg-black/0']"
      @click="toggleFilter"
    >
      <div
        class="h-1 rounded-full absolute bg-white transition-all left-1/2 top-1/2 origin-left"
        :class="[showFilter ? 'w-2/3' : 'w-1/3']"
        :style="{
          transform: showFilter
            ? 'rotate(45deg) translateX(-50%) translateY(-50%)'
            : 'rotate(45deg) translateX(0%) translateY(-50%)',
        }"
      ></div>
      <div
        class="h-1 rounded-full absolute bg-white transition-all left-1/2 top-1/2 -translate-y-1/2 -translate-x-1/2 -rotate-45"
        :class="[showFilter ? 'w-2/3' : 'w-0']"
      ></div>
      <div
        class="w-[40%] aspect-square rounded-full border-2 border-white origin-right absolute rotate-45 transition-all"
        :class="showFilter ? 'top-0 -translate-y-full right-full' : 'top-1/2 -translate-y-1/2 right-1/2'"
      ></div>
    </div>
  </div>
</template>
