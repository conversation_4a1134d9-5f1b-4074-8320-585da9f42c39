<script setup lang="ts">
import CityList                     from '@/components/praytimes/CityList.vue';
import CompassComponent             from '@/components/praytimes/CompassComponent.vue';
import CountryList                  from '@/components/praytimes/CountryList.vue';
import ContinentSelector            from '@/components/praytimes/ContinentSelector.vue';
import LocationSearcher             from '@/components/praytimes/LocationSearcher.vue';
import PrayTimesDBService           from '@/services/db/prayTimesDbService.ts';
import type City                    from '@/models/City.ts';
import type Continent               from '@/models/Continent.ts';
import type Country                 from '@/models/Country.ts';
import { ref, computed, onMounted } from 'vue';
import { storeToRefs }              from 'pinia';
import { useCityStore }             from '@/stores/CityStore.ts';
import { useContinentStore }        from '@/stores/ContinentStore.ts';
import { useCountryStore }          from '@/stores/CountryStore.ts';
import { usePrayersScheduleStore }  from '@/stores/pray-times/PrayersScheduleStore.ts';
import { sortBy }                   from '@/utils/utils.ts';

const MECCA_ID = 193;

const { continents } = storeToRefs(useContinentStore());
const prayersScheduleStore = usePrayersScheduleStore();

const favoriteContinentId = parseInt(localStorage.getItem('continent') ?? '4');
const favoriteCountryId = localStorage.getItem('country') ? parseInt(`${localStorage.getItem('country')}`) : undefined;
const favoriteCityId = localStorage.getItem('city') ? parseInt(`${localStorage.getItem('city')}`) : undefined;

const currentContinent = ref(continents.value.find(({ id }: { id: number }) => id === favoriteContinentId));

const currentCountry = ref<Country>();
const currentCity = ref<City>();

const filter = ref('');
const filterIsActive = ref(false);
const isSelectingCity = ref(true);

const locationSearcher = ref(null);

const sortedCities = computed(() => {
  const cities = currentCountry.value?.cities;

  if (!cities) return [];

  return cities.concat().sort(sortBy('name'));
});

const sortedCountries = computed(() => {
  const countries = currentContinent.value!.countries;

  return countries?.concat()?.sort(({ name: name1 }, { name: name2 }) => (name1 > name2 ? 1 : -1)) ?? [];
});

const departureY = ref(0);
const targetY = ref(0);

const arrowLength = computed(() => Math.abs(targetY.value - departureY.value));
const arrowTop = computed(() => Math.min(departureY.value, targetY.value));

function getArrowStartY() {
  const currentCountryMenuItem = document.querySelector(`[data-query="${currentCountry.value?.name}"]`);

  const bbox: DOMRect | undefined = currentCountryMenuItem?.getBoundingClientRect?.();

  if (!bbox) return 0;

  const itemMidpoint = (bbox.top + bbox.bottom) / 2;

  const countriesList = document.querySelector('[data-query="countries-list"]');

  const parentTop: number = countriesList?.getBoundingClientRect?.()?.top ?? 0;

  return itemMidpoint - parentTop;
}

function getArrowEndY() {
  const currentCityMenuItem = document.querySelector(`[data-query="${currentCity.value?.name}"]`);

  const bbox: DOMRect | undefined = currentCityMenuItem?.getBoundingClientRect?.();

  if (!bbox) return departureY.value ?? 0;

  const itemMidpoint = (bbox.top + bbox.bottom) / 2;

  const citiesList = document.querySelector('[data-query="cities-list"]');

  const parentTop: number = citiesList?.getBoundingClientRect?.()?.top ?? departureY.value ?? 0;

  return itemMidpoint - parentTop;
}

function setCurrentCity(city: City | undefined, force = false) {
  if (!city) return;

  prayersScheduleStore.flush();
  clearFilter();

  isSelectingCity.value = false;

  if (city === currentCity.value && !force) return;

  currentCity.value = city;
  localStorage.setItem('city', city.id.toString());

  PrayTimesDBService.getYearlyScheduleForCity(city);

  updateArrow();
}

function setCurrentCountry(country: Country | undefined) {
  if (!country) return;

  clearFilter();

  currentCity.value = undefined;
  currentCountry.value = country;

  localStorage.removeItem('city');
  localStorage.setItem('country', country.id.toString());

  PrayTimesDBService.inflate();

  updateArrow();
}

function clearFilter() {
  (locationSearcher.value as any)?.clearFilter?.();
}

function toggleCitySelector() {
  if (!isSelectingCity.value) {
    isSelectingCity.value = true;
    return;
  }

  if (!currentCity.value) return;

  isSelectingCity.value = false;
}

function updateArrow() {
  departureY.value = getArrowStartY();
  targetY.value = getArrowEndY();
}

function setCurrentContinent(continent: Continent) {
  localStorage.setItem('continent', continent!.id.toString());
  localStorage.removeItem('country');
  localStorage.removeItem('city');

  currentContinent.value = continent;
  currentCountry.value = undefined;
  currentCity.value = undefined;

  updateArrow();

  PrayTimesDBService.inflate();
}

function resurrectCountry() {
  if (!favoriteCountryId) return;

  const country = useCountryStore().countries.find(({ id }: { id: number }) => id === favoriteCountryId);

  if (country) {
    setCurrentCountry(country as any);
    resurrectCity();
  } else {
    setTimeout(resurrectCountry, 250);
  }
}

function resurrectCity() {
  if (!favoriteCityId) return;

  const city = useCityStore().cities.find((city: City) => city.id === favoriteCityId);
  if (city) {
    setCurrentCity(city);
  } else {
    setTimeout(resurrectCity, 250);
  }
}

onMounted(() => {
  if (!localStorage.getItem('continent')) {
    localStorage.setItem('continent', favoriteContinentId.toString());
  }

  resurrectCountry();
});

defineExpose({ currentCity, isSelectingCity, setCurrentCity });
</script>

<template>
  <div
    class="w-full flex flex-col transition-all duration-500 ease-in"
    :class="isSelectingCity ? 'h-[100%]' : 'h-24'"
  >
    <div
      class="flex-none flex flex-row p-4 h-24 overflow-hidden shadow-xl z-20 secondary"
      :class="isSelectingCity ? 'gap-0' : 'gap-6'"
      @click.stop="toggleCitySelector"
    >
      <div
        class="h-full flex items-center transition-all duration-700 ease"
        :class="isSelectingCity ? 'w-0' : 'w-1/6'"
      >
        <img
          :src="currentCountry?.flagUrl"
          class="transition-all duration-500 ease"
          :class="isSelectingCity ? '-translate-x-full opacity-0' : ''"
        />
      </div>

      <div class="flex-1 overflow-hidden flex flex-col gap-1 justify-center">
        <h3
          class="w-full overflow-hidden whitespace-nowrap overflow-ellipsis text-3xl font-bold transition-all"
          :class="{ 'animate-pulse font-bold': !currentCity }"
        >
          {{ currentCity?.name || (currentCountry ? 'Select a city' : '') }}
        </h3>
        <h6
          class="w-fit"
          :class="currentCountry ? 'text-sm' : 'text-3xl animate-pulse font-bold'"
        >
          {{ currentCountry?.name || 'Select a country' }}
        </h6>
      </div>

      <ContinentSelector
        :class="{ 'opacity-0 -translate-y-full hide-me': !isSelectingCity }"
        :continents="continents"
        :selected="currentContinent"
        @change="setCurrentContinent"
      />

      <CompassComponent
        v-if="!isSelectingCity && currentCity?.id !== MECCA_ID"
        :city="currentCity"
      />
    </div>

    <div
      class="relative overflow-hidden flex p-2 pt-1 transition-all duration-500 ease"
      :class="isSelectingCity ? 'grow-[1] h-[100%] opacity-1' : 'grow-[0] h-0 opacity-0 -translate-y-full'"
    >
      <div class="w-full h-full overflow-hidden flex flex-col secondary px-0">
        <div class="py-4 text-center border-b-2 h-16">Countries</div>

        <CountryList
          :dataQueryKey="'name'"
          :filter="filterIsActive && !currentCountry ? filter : ''"
          :inProgress="false"
          :itemKey="'id'"
          :itemLabel="'name'"
          :items="sortedCountries"
          :selectedItem="currentCountry"
          @scroll.passive="updateArrow"
          @select="setCurrentCountry"
          class="w-full h-full overflow-auto secondary px-0 non-separated"
          data-query="countries-list"
        />
      </div>

      <div
        class="relative h-full flex flex-col z-20"
        :class="currentCountry ? 'w-4' : 'w-0'"
      >
        <div class="h-16 py-4 border-b-2"></div>

        <div class="relative w-full h-full primary">
          <div
            class="absolute left-1/2 right-0 px-1/4 border-l-2 border-dashed secondary w-0 translate-x-1 flex flex-col"
            :style="`top: ${arrowTop}px; height: ${arrowLength}px`"
          >
            &nbsp;
          </div>
        </div>
      </div>

      <div
        class="relative h-full overflow-hidden flex flex-col secondary px-0 transition-all duration-500 ease"
        :class="currentCountry ? 'w-[100%]' : 'w-0'"
      >
        <div class="py-4 text-center border-b-2 h-16">Cities</div>

        <CityList
          :class="currentCountry ? 'w-[100%]' : 'w-0'"
          :dataQueryKey="'name'"
          :filter="filterIsActive ? filter : ''"
          :inProgress="!!currentCountry?.isBeingUpdated"
          :itemKey="'id'"
          :itemLabel="'name'"
          :items="sortedCities"
          :selectedItem="currentCity"
          @scroll.passive="updateArrow"
          @select="setCurrentCity"
          class="h-full overflow-auto relative pr-2 primary non-separated z-10"
          data-query="cities-list"
        />
      </div>

      <LocationSearcher
        v-if="!currentCountry?.isBeingUpdated"
        class="absolute bottom-8 right-4 z-20"
        ref="locationSearcher"
        :initialValue="filter"
        @filter="(value: string) => (filter = value)"
        @filter-toggle="(value: boolean) => (filterIsActive = value)"
      />
    </div>
  </div>
</template>

<style>
select {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAUCAMAAACtdX32AAAAdVBMVEUAAAD///8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAhMdQaAAAAJ3RSTlMAAAECAwQGBwsOFBwkJTg5RUZ4eYCHkJefpaytrsXGy8zW3+Do8vNn0bsyAAAAYElEQVR42tXROwJDQAAA0Ymw1p9kiT+L5P5HVEi3qJn2lcPjtIuzUIJ/rhIGy762N3XaThqMN1ZPALsZPEzG1x8LrFL77DHBnEMxBewz0fJ6LyFHTPL7xhwzWYrJ9z22AqmQBV757MHfAAAAAElFTkSuQmCC);
  background-position: 100%;
  background-repeat: no-repeat;
  padding: 0.5rem;
}
.hide-me {
  animation: hide-me 0.25s forwards;
}

@keyframes hide-me {
  100% {
    position: absolute;
  }
}
</style>
