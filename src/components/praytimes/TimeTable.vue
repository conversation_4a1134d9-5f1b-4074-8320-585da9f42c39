<script setup lang="ts">
import type { PrayersTimeTable, PrayersTimesSource } from '@/types.ts';
import { computed, onMounted, onUnmounted, ref } from 'vue';
import TimeSlot from '@/components/praytimes/TimeSlot.vue';
import DateInput from '@/components/ui/DateInput.vue';

const emit = defineEmits(['dateSelect', 'showGraph']);

const props = defineProps<{
  date: Date;
  observesSummerTime: boolean;
  timeTable: PrayersTimeTable | undefined;
  source: PrayersTimesSource;
}>();

const currentYear = new Date().getFullYear();
const nextYear = currentYear + 1;
const currentYearStart = new Date(`${currentYear}-1-1`);
const currentYearEnd = new Date(`${nextYear}-1-1`);
currentYearEnd.setDate(currentYearEnd.getDate() - 1);

const azanables = ['fajr', 'dhohr', 'aser', 'maghreb', 'isha'];

const ticker = ref(0);
const applySummerTime = ref(false);

const isToday = computed(() => new Date().setHours(0, 0, 0, 0) == new Date(props.date).setHours(0, 0, 0, 0));

const availableTimeSlots = computed(() => {
  /**
   * * Theis will forcefuly trigger this computed property once per second.
   * * Consequently, updated data will be sent to the timeslots
   */
  if (ticker.value < 0) return [];

  const timeTable = { ...(props.timeTable ?? {}) };

  if (!timeTable['fajr']) return [];

  if (timeTable['emsak']?.valueOf() === timeTable['fajr'].valueOf()) delete timeTable['emsak'];

  return Object.keys(timeTable)
    .map(key => [key, (timeTable as any)[key]])
    .filter(([, value]) => value !== null)
    .sort(([, value1], [, value2]) => (value1 > value2 ? 1 : -1))
    .map(([k, v]) => [k, new Date(v.getTime() + (props.observesSummerTime && applySummerTime.value ? 3600 : 0) * 1000)]);
});

const durations = computed(() => {
  const array: number[] = [];
  const slots = availableTimeSlots.value;
  slots.forEach((_, index) => {
    array.push((slots[index + 1]?.[1] ?? slots[index][1]) - slots[index][1]);
  });
  return array;
});

let tickerInterval: ReturnType<typeof setInterval>;

const today = {
  get end() {
    const date = new Date();
    date.setHours(23);
    date.setMinutes(59);
    date.setSeconds(50);

    return date;
  },

  get beginning() {
    const date = new Date();
    date.setHours(0);
    date.setMinutes(0);
    date.setSeconds(0);

    return date;
  },
};

function onDateChange(date: Date) {
  emit('dateSelect', date);
}

function pageRefresh() {
  location.reload();
}

function storeSummerTimeSetting(event: Event) {
  const { checked } = event.currentTarget as HTMLInputElement;

  applySummerTime.value = checked;

  localStorage.setItem('applySummerTime', String(applySummerTime.value));
}

onMounted(() => {
  tickerInterval = setInterval(() => ticker.value++, 1 * 1000);

  applySummerTime.value = Boolean(JSON.parse(localStorage.getItem('applySummerTime') ?? 'false'));
});

onUnmounted(() => clearInterval(tickerInterval));
</script>

<template>
  <section
    v-if="availableTimeSlots.length"
    class="flex-1 flex flex-col relative pt-12 pb-0 overflow-hidden"
  >
    <div
      v-for="([key, value], index) in availableTimeSlots"
      :key="key"
      class="passive flex flex-1 items-center relative"
    >
      <TimeSlot
        :label="key"
        :start="value"
        :end="index < availableTimeSlots.length - 1 ? availableTimeSlots[index + 1][1] : today.end"
        :isAzanable="azanables.includes(key)"
      />
    </div>

    <!-- Date and source -->
    <div class="flex-0 flex items-center p-4 pt-0 text-black/50 w-full">
      <!-- {{ getCurrentTimezone() }} -->
      <div class="flex-1 flex flex-col gap-2 items-center">
        <DateInput
          :date="$props.date"
          :earliest="currentYearStart"
          :latest="currentYearEnd"
          @change="onDateChange"
          :warning="!isToday"
        />

        <!-- Summer time toggle -->
        <div v-if="observesSummerTime">
          <label class="flex items-center gap-2 p-2">
            <input
              type="checkbox"
              class="w-6 h-6"
              v-model="applySummerTime"
              @change="storeSummerTimeSetting"
            />
            Summer Time
          </label>
        </div>

        <div class="text-xs flex gap-1 items-center">
          <span>Source:</span>
          <a
            v-if="source.url"
            :href="source.url"
            target="_blank"
            rel="noreferrer noopener"
            class="underline"
            >{{ source.name }}</a
          >
          <span v-else>{{ source.name }}</span>
        </div>
      </div>

      <button
        @click.stop="$emit('showGraph')"
        class="w-10 h-10 rounded-full bg-emerald-200/25"
      >
        <img src="/img/icons/graph.svg" />
      </button>
    </div>
  </section>

  <div
    v-else
    class="h-full flex flex-col gap-8 items-center justify-center"
  >
    <span class="text-xl font-bold">Nothing to show, yet!</span>
    <button
      class="primary p-2 rounded font-semibold"
      @click="pageRefresh"
    >
      Try again!
    </button>
  </div>
</template>
