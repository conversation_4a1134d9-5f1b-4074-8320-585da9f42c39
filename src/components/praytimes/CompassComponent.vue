<script setup lang="ts">
import type City from '@/models/City.ts';
import { computed, onMounted, ref } from 'vue';

const props = defineProps<{
  city: City | undefined;
}>();

const KARBALA = { latitude: 32.6167, longitude: 44.0333 };
const MECCA = { latitude: 21.42664, longitude: 39.82536 };
const KABA = { latitude: 21.4226897, longitude: 39.824962 };
const QUDS = { latitude: 31.7761, longitude: 35.2336 };

function toRadians(degrees: number) {
  return degrees * (Math.PI / 180);
}

function toDegrees(radians: number) {
  return radians * (180 / Math.PI);
}

function bearing({ latitude, longitude }: { latitude: number; longitude: number }) {
  if (!props.city?.latitude) return 0;

  // User's location
  const startLat = toRadians(props.city.latitude);
  const startLng = toRadians(props.city.longitude);

  const destLat = toRadians(latitude);
  const destLng = toRadians(longitude);

  const y = Math.sin(destLng - startLng) * Math.cos(destLat);
  const x = Math.cos(startLat) * Math.sin(destLat) - Math.sin(startLat) * Math.cos(destLat) * Math.cos(destLng - startLng);

  const brng = toDegrees(Math.atan2(y, x));

  return (brng + 360) % 360;
}

const shallAdjust = false;
const northPoleAdjustmentAngle = 11.5;

const adjustmentAngle = computed(() => {
  return shallAdjust ? northPoleAdjustmentAngle : 0;
});

const karbalaBearing = computed(() => {
  return bearing(KARBALA) - adjustmentAngle.value;
});

const meccaBearing = computed(() => {
  return bearing(MECCA) - adjustmentAngle.value;
});

const kabaBearing = computed(() => {
  return bearing(KABA) - adjustmentAngle.value;
});

const qudsBearing = computed(() => {
  return bearing(QUDS) - adjustmentAngle.value;
});

function iOS() {
  if (typeof DeviceOrientationEvent === 'undefined') return false;

  return typeof (DeviceOrientationEvent as any).requestPermission === 'function';
}

const absolute = ref(false);
const alpha = ref(0);
const beta = ref(0);
const gamma = ref(0);
const webkitCompassHeading = ref<number | null>(null);

function handleOrientation(event: DeviceOrientationEvent) {
  absolute.value = event.absolute;
  webkitCompassHeading.value = (event as any).webkitCompassHeading;
  alpha.value = event.alpha ?? 0;
  beta.value = event.beta ?? 0;
  gamma.value = event.gamma ?? 0;
}

async function requestDeviceOrientation() {
  if (!iOS()) return;

  try {
    const permissionState = await (DeviceOrientationEvent as any).requestPermission();
    if (permissionState === 'granted') {
      localStorage.setItem('deviceOrientationPermission', 'granted');
      window.removeEventListener('deviceorientationabsolute', handleOrientation as EventListenerOrEventListenerObject, true);
      window.addEventListener('deviceorientation', handleOrientation);
    } else {
      localStorage.removeItem('deviceOrientationPermission');
    }
  } catch (exc) {
    //
  }
}

const rotation = computed(() => {
  if (iOS()) return -(webkitCompassHeading.value ?? 0);

  return webkitCompassHeading.value || alpha.value || 0;
});

function deviceOrientationPermissionGranted() {
  return localStorage.getItem('deviceOrientationPermission') === 'granted';
}

onMounted(() => {
  if (deviceOrientationPermissionGranted()) {
    window.addEventListener('deviceorientation', handleOrientation);
  } else if ('ondeviceorientationabsolute' in window) {
    // We can listen for the new deviceorientationabsolute event.
    window.addEventListener('deviceorientationabsolute', handleOrientation as EventListenerOrEventListenerObject, true);
  }
});
</script>

<template>
  <div
    class="h-full aspect-square rounded-full relative bg-contain"
    style="background-image: url(img/compass-rose-2.png)"
    :style="`rotate: ${rotation - 90}deg`"
  >
    <div
      class="w-full h-full relative"
      :style="`rotate: ${kabaBearing}deg`"
    >
      <div class="absolute left-1/2 top-1/2 -translate-y-1/2 w-[40%] h-[2px] bg-white"></div>
      <div
        class="w-2 h-2 border-white border-t-0 border-r-[0.2rem] border-b-[0.2rem] border-l-0 absolute left-[80%] top-1/2 -translate-y-1/2 -rotate-45"
      ></div>
    </div>
  </div>
</template>
