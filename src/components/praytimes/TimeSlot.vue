<script setup lang="ts">
import { computed, onMounted, ref, Ref } from 'vue';

const locale = 'en-US';

const props = defineProps<{
  isAzanable: boolean;
  label: string;
  start: Date;
  end: Date;
}>();

function getTimeAgo(d: Date) {
  const now = new Date();
  const formatter = new Intl.RelativeTimeFormat(locale, { numeric: 'auto' });

  let diffInDays = 0;
  let diffInHours = 0;
  let diffInMinutes = 0;

  const diffInMilliseconds = +d - +now;
  const diffInSeconds = Math.round(diffInMilliseconds / 1000);
  const absoluteDiff = Math.abs(diffInSeconds);

  switch (true) {
    case absoluteDiff < 60:
      // Time difference is within 60 seconds
      return formatter.format(diffInSeconds, 'seconds');
    case absoluteDiff < 3600:
      // Time difference is within 60 minutes
      diffInMinutes = Math.round(diffInSeconds / 60);
      return formatter.format(diffInMinutes, 'minutes');
    case absoluteDiff < 86400:
      // Time difference is within 24 hours
      diffInHours = Math.round(diffInSeconds / 3600);
      return formatter.format(diffInHours, 'hours');
    default:
      // Time difference is larger than 24 hours
      diffInDays = Math.round(diffInSeconds / 86400);
      return formatter.format(diffInDays, 'days');
  }
}

const nowRatio = computed(() => {
  const now = new Date();

  const elapsedTime = +new Date() - +props.start;
  const duration = +props.end - +props.start;
  const ratio = elapsedTime / duration;

  return { now, ratio };
});

const azanPlayer: Ref<HTMLAudioElement | null> = ref(null);

const audioElementAvailabilityDuration = 5; // minutes
const isPast = computed(() => new Date() > props.end);
const isFuture = computed(() => new Date() < props.start);
const isCurrent = computed(() => !isPast.value && !isFuture.value);
const isClose = computed(() => Math.abs(+props.start - +new Date()) < 30 * 60 * 1000);
const isRecent = computed(() => Math.abs(+props.start - +new Date()) < audioElementAvailabilityDuration * 60 * 1000);
const muted = ref(false);
const isPausedByUser = ref(false);
const errorMessage = ref('');

function t(key: string) {
  const translations = {
    'en-US': {
      emsak: 'astention',
      fajr: 'dawn',
      sunrise: 'sunrise',
      dhohr: 'noon',
      aser: 'afternoon',
      sunset: 'sunset',
      maghreb: 'dusk',
      isha: 'dinner',
      midnight: 'midnight',
    },
  };

  return (translations[locale] as Record<string, string>)[key];
}

function toggleAzan() {
  muted.value = !muted.value;

  const settings = JSON.parse(localStorage.getItem('azan-settings') ?? '{}');
  settings[props.label] = muted.value;
  localStorage.setItem('azan-settings', JSON.stringify(settings));
}

function setAzanPalyerTime() {
  if (!azanPlayer.value) return;

  const elapsed = +new Date() - +props.start; // Milliseconds
  azanPlayer.value.currentTime = elapsed / 1000;
}

onMounted(() => {
  const settings = JSON.parse(localStorage.getItem('azan-settings') ?? '{}');

  muted.value = Boolean(settings[props.label] ?? false);

  const element = azanPlayer.value;

  element?.addEventListener('play', () => {
    isPausedByUser.value = false;
    errorMessage.value = '';
    setAzanPalyerTime();
  });

  // Try playing after ensuring it's loaded
  element?.addEventListener('canplaythrough', () => {
    if (isPausedByUser.value || muted.value) return;

    element.play().catch(error => {
      errorMessage.value = error.name === 'NotAllowedError'
        ? 'Please click to play azan'
        : 'Something went wrong';
    });
  });
});
</script>

<template>
  <div
    class="flex-1 h-full relative pt-4"
    :class="{
      'text-bold': isCurrent && (isAzanable || isClose),
      'text-yellow-300': isCurrent && isAzanable,
      'text-black': isCurrent && !isAzanable,
      'text-black/50': isPast,
      'text-white/75': isFuture && isClose,
      'text-black/75': isFuture,
      'border-black/10': isPast,
      'border-black/25': isFuture,
      'border-black/50': isCurrent,
    }"
  >
    <div
      v-if="isCurrent"
      class="absolute inset-x-0 top-0 -bottom-96 -translate-y-48 z-10 pointer-events-none"
      style="
        background: linear-gradient(
          to bottom,
          rgba(0, 121, 107, 0) 0%,
          rgba(30, 199, 179, 0.5) 50%,
          rgba(0, 121, 107, 0) 100%
        );
      "
    ></div>

    <div
      v-if="isCurrent"
      class="absolute z-20 inset-x-0 border-dashed flex items-baseline transition-all -translate-y-full bg-gradient-to-b"
      :style="`top: ${100 * nowRatio.ratio}%`"
    >
      <hr class="border-dashed flex-1 border-black/25" />
      <div class="font-mono font-thin text-sm md:tracking-widest text-black/50 whitespace-nowrap flex gap-2 h-0 px-2">
        <span class="hidden sm:inline">
          {{ Intl.DateTimeFormat(locale, { hour: '2-digit', minute: 'numeric', second: 'numeric', hourCycle: 'h23' }).format(nowRatio.now) }}
        </span>
        <span class="pe-2 sm:hidden">now</span>
      </div>
      <hr class="border-dashed hidden lg:inline w-8 border-black/25" />
    </div>

    <div class="flex items-center justify-center content-start gap-1 z-30 absolute top-0 -translate-y-full w-full">
      <div
        class="flex-1 text-2xl text-end flex items-baseline"
        style="font-family: monospace"
      >
        <hr
          class="flex-1 border-dashed"
          :class="{
            'border-black/10': isPast,
            'border-black/50': isCurrent,
            'border-black/25': isFuture,
          }"
        >
        <span class="px-2 h-0">{{ Intl.DateTimeFormat(locale, { hour: '2-digit', minute: 'numeric', hourCycle: 'h23' }).format(start) }}</span>
      </div>

      <div
        class="flex-1 text-xl text-start flex items-baseline capitalize"
        style="font-family: futura, verdana, monospace"
      >
        <span class="px-2 h-0">{{ t(label) }}</span>
        <hr
          class="flex-1 border-dashed"
          :class="{
            'border-black/10': isPast,
            'border-black/50': isCurrent,
            'border-black/25': isFuture,
          }"
        />
      </div>

      <button
        class="absolute left-4 top-full -translate-y-1/2 h-8"
        @click="toggleAzan"
      >
        <img
          v-if="isAzanable"
          class="h-full"
          :src="`img/icons/speaker-${muted ? 'muted' : 'active'}.svg`"
        />
      </button>
    </div>

    <div
      v-if="(isCurrent && isAzanable) || isClose"
      class="text-center font-light text-md -mt-3"
    >
      {{ getTimeAgo(start) }}
    </div>

    <center v-if="errorMessage">{{ errorMessage }}</center>

    <audio
      v-if="isCurrent && isAzanable && isRecent"
      ref="azanPlayer"
      controls
      :autoplay="!muted"
      :muted="muted"
      class="mx-auto mix-blend-lighten h-8 mt-1 mb-8"
      src="/sound/pray-times/azan/mehrdad.mp4"
      type="audio/mpeg"
    ></audio>
  </div>
</template>
