<script setup lang="ts">
import City from '@/models/City.ts';
import PrayTimesDBService from '@/services/db/prayTimesDbService.ts';
import { onMounted, ref } from 'vue';
import { usePrayersScheduleStore } from '@/stores/pray-times/PrayersScheduleStore.ts';

defineEmits(['close']);

const props = defineProps<{
  city: City;
}>();

const prayersScheduleStore = usePrayersScheduleStore();

const selectedTimes = ref<string[]>([]);
const showOptions = ref(false);

const availableTimes = [
  { key: 'fajr', label: 'dawn', color: 'cyan' },
  { key: 'sunrise', label: 'sunrise', color: 'cornsilk' },
  { key: 'dhohr', label: 'noon', color: 'yellow' },
  { key: 'aser', label: 'afternoon', color: 'pink' },
  { key: 'sunset', label: 'sunset', color: 'orange' },
  { key: 'maghreb', label: 'dusk', color: 'cadetblue' },
  { key: 'isha', label: 'dinner', color: 'cornflowerblue' },
];

function draw() {
  const graphHolder: HTMLDivElement = document.querySelector('#graph-holder')!;

  const canvas = document.querySelector('canvas')!;
  canvas.height = graphHolder.clientHeight;
  canvas.width = graphHolder.clientWidth;

  const ctx = canvas.getContext('2d')!;
  ctx.translate(0.5, 0.5);

  ctx.clearRect(0, 0, canvas.width, canvas.height);

  drawHours(canvas);

  const columnWidth = canvas.width / 364;

  selectedTimes.value.forEach((time, index) => {
    ctx.moveTo(0, canvas.height);
    ctx.strokeStyle = availableTimes.find(t => t.key === time)?.color ?? 'black';
    ctx.lineWidth = 3;

    const values: number[] = prayersScheduleStore.schedules.map((schedule: any) => {
      const date = new Date(schedule.timeTable[time]);
      return date.getMinutes() + 60 * date.getHours();
    });

    // Each value is between 0 and 1440
    const amplitude = 1440;
    const amplifiedValues = values.map(value => (value / amplitude) * canvas.height);

    ctx.beginPath();
    amplifiedValues.forEach((value, index) => {
      const y = canvas.height - value;

      index === 0 ? ctx.moveTo(0, y) : ctx.lineTo(index * columnWidth, y);
    });

    ctx.stroke();
    ctx.closePath();
  });
}

function drawHours(canvas: HTMLCanvasElement) {
  const ctx = canvas.getContext('2d')!;
  ctx.translate(0.5, 0.5);
  ctx.setLineDash([2, 4]);
  ctx.strokeStyle = '#063E3A';

  const textOffset = 8;
  ctx.font = '10px Arial';

  for (let hour = 0; hour <= 24; hour += 6) {
    const y = canvas.height - (hour * canvas.height) / 24;
    ctx.fillText(`${hour}:00`, textOffset, y);
    ctx.moveTo(0, y);
    ctx.lineTo(canvas.width, y);
    ctx.stroke();
  }
}

function toggleKey(key: string) {
  if (selectedTimes.value.includes(key)) {
    selectedTimes.value = selectedTimes.value.filter(k => k !== key);
  } else {
    selectedTimes.value.push(key);
  }

  draw();
}

onMounted(async () => {
  draw();
  await PrayTimesDBService.getYearlyScheduleForCity(props.city, new Date());
  availableTimes.forEach(at => toggleKey(at.key));
});
</script>

<template>
  <div class="w-full h-full relative bg-teal-900 flex flex-col">
    <div class="flex flex-col gap-4 items-center px-8 py-4 bg-black/25">
      <div class="w-full flex justify-between">
        <button
          @click.stop="$emit('close')"
          class="text-xl bg-black/50 w-8 h-8 rounded-full"
        >
          &larr;
        </button>

        <button @click.stop="showOptions = !showOptions">settings</button>
      </div>

      <div
        class="flex-1 flex flex-wrap gap-2"
        v-if="showOptions"
      >
        <label
          v-for="time in availableTimes"
          :key="time.key"
          class="flex gap-2 items-center p-2 rounded"
          :style="{ background: time.color }"
        >
          <input
            type="checkbox"
            class="w-4 h-4"
            :checked="selectedTimes.includes(time.key)"
            @click.stop="toggleKey(time.key)"
          />
          <span class="text-black">{{ time.label }}</span>
        </label>
      </div>
    </div>

    <div
      id="graph-holder"
      class="flex-1"
    >
      <canvas></canvas>
    </div>
  </div>
</template>
