<script setup lang="ts">
import { computed } from 'vue';
import ProgressIndicator from '@/components/ui/ProgressIndicator.vue';

defineEmits(['select']);

const props = defineProps<{
  dataQueryKey: string;
  filter: string;
  inProgress: Boolean;
  itemKey: string;
  itemLabel: string;
  items: any[];
  selectedItem: any;
}>();

const filteredItems = computed(() => {
  if (!props || !props.filter) return props.items;

  const regexp = new RegExp(Array.from(props.filter).join('.*'), 'i');

  return props.items?.filter(item => regexp.test(item[props.itemLabel])) ?? [];
});

const keyPrefix = window.crypto.randomUUID?.() ?? window.crypto.getRandomValues(new Uint32Array(1))[0];
</script>

<template>
  <ul style="position: unset">
    <ProgressIndicator v-if="inProgress" />

    <li
      v-for="item in filteredItems"
      :key="`${keyPrefix}-${item[itemKey]}`"
      class="py-4 pl-8 pr-2 cursor-pointer transition-all ease"
      :class="{
        'passive shadow-md sticky top-0 bottom-14 drop-shadow-lg selected primary z-10': item.id === selectedItem?.id,
      }"
      @click.stop="$emit('select', item)"
      :data-query="`${item[dataQueryKey]}`"
    >
      <span class="w-full">
        <span>{{ item[itemLabel] }}</span>
        <span
          v-if="item.id === selectedItem?.id"
          class="absolute right-0"
        >
          &rarr;
        </span>
      </span>
    </li>
  </ul>
</template>
