import { Point } from '@/types/types-quran.ts';
import QuranDbService from '@/services/db/quran/quran-db-service.ts';
import { compact, uniq } from '@/utils/utils.ts';
import Verse from './Verse.ts';
import Chapter from './Chapter.ts';
import VerseUtils from '@/utils/verse-utils.ts';

export const TOTAL_PAGES = 604;
export interface VerseDetails {
  endSignCoordinates?: Point;
}

interface PageConstructorOptions {
  index: number;
  verseDetails: Record<number, VerseDetails | {}>;
}

export default class Page {
  index: number;
  verseDetails: Record<number, VerseDetails>;

  constructor(options: PageConstructorOptions) {
    this.index = options.index;
    this.verseDetails = options.verseDetails;
  }

  get hasDetails() {
    return Object.values(this.verseDetails).every(verseDetails => verseDetails.endSignCoordinates);
  }

  get verses(): Verse[] {
    const verses = Object.keys(this.verseDetails)
      .map(indexInQuran => parseInt(indexInQuran))
      .map(indexInQuran => VerseUtils.findVerse(indexInQuran));

    return verses as Verse[];
  }

  get part() {
    return Math.max(...this.verses.map(verse => verse?.part)) || 1;
  }

  get chapters(): Chapter[] {
    return compact(uniq(this.verses.map(verse => verse?.chapter)));
  }

  loadDetails() {
    if (this.hasDetails) return;

    QuranDbService.fetchPageDetails(this.index);
  }
}
