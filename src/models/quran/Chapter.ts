import { useVerseStore } from '@/stores/quran/verse-store.ts';
import Verse from './Verse.ts';

interface ChapterConstructorOptions {
  englishTitle: string;
  englishTitleTranslation: string;
  index: number;
  title: string;
}

export default class Chapter {
  index: number;
  title: string;
  englishTitle: string;
  englishTitleTranslation: string;

  private _verses: Verse[] = [];

  constructor(options: ChapterConstructorOptions) {
    this.index = options.index;
    this.title = options.title;
    this.englishTitle = options.englishTitle;
    this.englishTitleTranslation = options.englishTitleTranslation;
  }

  get verses(): Verse[] {
    if (this._verses.length === 0) {
      this._verses = (useVerseStore().verses as Verse[]).filter(verse => verse.chapter.index === this.index);
    }

    return this._verses;
  }
}
