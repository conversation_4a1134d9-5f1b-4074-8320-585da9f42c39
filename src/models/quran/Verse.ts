import { Point } from '@/types/types-quran.ts';
import { useChapterStore } from '@/stores/quran/chapter-store.ts';
import { usePageStore } from '@/stores/quran/page-store.ts';
import Chapter from './Chapter.ts';
import Page from './Page.ts';
import StringUtils from '@/utils/string-utils.ts';

interface VerseConstructorOptions {
  chapterIndex: number;
  endSignCoordinates?: Point;
  hizbQuarter: number;
  index: number;
  indexInQuran: number;
  juz: number;
  manzil: number;
  ruku: number;
  text: string;
}

export default class Verse {
  chapterIndex: number;
  endSignCoordinates: Point | undefined;
  hizbQuarter: number;
  index: number;
  indexInQuran: number;
  juz: number;
  manzil: number;
  ruku: number;
  text: string;

  private _purifiedText: string;
  private _page: Page | undefined;

  private _partStartVerceIndices = [1, 149, 260, 386, 517, 641, 736, 900, 985, 1201, 1328, 1479, 1649, 1803, 2030, 2215, 2484, 2674, 2876, 3215, 3386, 3551, 3733, 4090, 4265, 4511, 4706, 5105, 5242, 5673];

  constructor(options: VerseConstructorOptions) {
    this.chapterIndex = options.chapterIndex;
    this.endSignCoordinates = options.endSignCoordinates ?? undefined;
    this.hizbQuarter = options.hizbQuarter;
    this.index = options.index;
    this.indexInQuran = options.indexInQuran;
    this.juz = options.juz;
    this.manzil = options.manzil;
    this.ruku = options.ruku;
    this.text = options.text;

    this._purifiedText = new StringUtils().purify(this.text);
  }

  get chapter(): Chapter {
    return useChapterStore().chapters[this.chapterIndex - 1] as Chapter;
  }

  get page(): Page | undefined {
    this._page ??= this.findPage(usePageStore().pages as Page[]);

    return this._page;
  }

  get part() {
    return 30 - [...this._partStartVerceIndices].reverse().findIndex(startIndex => startIndex <= this.indexInQuran);
  }

  get purifiedText(): string {
    return this._purifiedText;
  }

  get purifiedWords(): string[] {
    return this.purifiedText.split(' ');
  }

  get id(): string {
    return `${this.chapter.index}:${this.index}`;
  }

  private findPage(pages: Page[]): Page {
    const middleIndex = Math.floor(pages.length / 2);
    const middlePage = pages[middleIndex];

    if (middlePage.verses.includes(this)) return middlePage;

    const pagesToSearch = middlePage.verses[0].indexInQuran > this.indexInQuran
      ? pages.slice(0, middleIndex)
      : pages.slice(middleIndex + 1);

    return this.findPage(pagesToSearch);
  }
}
