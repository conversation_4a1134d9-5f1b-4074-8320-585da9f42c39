import type Country from '@/models/Country.ts';
import TavoosModel from './TavoosModel.ts';
import { useCountryStore } from '@/stores/CountryStore.ts';

interface ConstructorObject {
  id: number;
  code: string;
  name: string;
}

export default class Continent extends TavoosModel {
  id: number;
  code: string;
  name: string;

  constructor(constructorObject: ConstructorObject) {
    super();

    this.id = constructorObject.id;
    this.code = constructorObject.code;
    this.name = constructorObject.name;
  }

  get countries(): Country[] {
    return useCountryStore().countries
      .filter(({ continentId }) => continentId == this.id)
      .map(x => x as any);
  }

  static from(continentData: { id: number; code: string; name: string }) {
    return new Continent(continentData as ConstructorObject);
  }
}
