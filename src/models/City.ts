import { useCityStore } from '@/stores/CityStore.ts';
import { useCountryStore } from '@/stores/CountryStore.ts';

interface ConstructorObject {
  id: number;
  latitude: number;
  longitude: number;
  name: string;
  countryId: number;
}

export default class City {
  id!: number;
  latitude!: number;
  longitude!: number;
  name!: string;
  countryId!: number;

  constructor(constructorObject: ConstructorObject) {
    const existingCity = useCityStore().cities.find((city: City) => city.id === constructorObject.id);

    if (existingCity) return existingCity;

    this.id = constructorObject.id;
    this.latitude = constructorObject.latitude;
    this.longitude = constructorObject.longitude;
    this.name = constructorObject.name;
    this.countryId = constructorObject.countryId;
  }

  get country() {
    return useCountryStore().countries.find(({ id }: { id: number }) => id === this.countryId);
  }
}
