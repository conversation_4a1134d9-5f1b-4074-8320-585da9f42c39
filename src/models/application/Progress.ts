import { reactive } from 'vue';

export default class Progress {
  state = reactive({
    currentValue: 0,
    min: 0,
    max: 0,
  });

  constructor(min: number = 0, max: number = 100, value: number = 0) {
    if (min === max) throw new Error('min and max can not be equal');

    this.state.min = min;
    this.state.max = max;
    this.state.currentValue = value;
  }

  increment(amount: number) {
    const destination = this.state.currentValue + amount;

    if (destination > this.state.max || destination < this.state.min) {
      throw new Error(`Invalid Process increment: ${amount}`);
    }

    this.state.currentValue = destination;
  }

  get currentValue() {
    return this.state.currentValue;
  }

  get completionRate() {
    return (this.state.currentValue - this.state.min) / (this.state.max - this.state.min);
  }

  complete() {
    this.state.currentValue = this.state.max;
  }

  reset() {
    this.state.currentValue = this.state.min;
  }
}
