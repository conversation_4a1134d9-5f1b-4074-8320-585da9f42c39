{"extends": "./tsconfig.base.json", "include": ["env.d.ts", "src/**/*", "src/**/*.vue"], "exclude": ["src/**/__tests__/*"], "compilerOptions": {"allowImportingTsExtensions": true, "declaration": true, "declarationDir": "dist/types", "emitDeclarationOnly": true, "forceConsistentCasingInFileNames": true, "module": "nodenext", "moduleResolution": "nodenext", "target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "strict": true}}